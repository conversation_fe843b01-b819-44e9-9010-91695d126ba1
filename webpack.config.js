const path = require('path');

module.exports = {
  mode: 'production',
  entry: './src/index.tsx',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    library: '@ourtrip/ui',
    libraryTarget: 'umd',
    globalObject: 'this',
    umdNamedDefine: true
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js']
  },
  externals: {
    react: {
      commonjs: 'react',
      commonjs2: 'react',
      amd: 'react',
      root: 'React'
    },
    'react-dom': {
      commonjs: 'react-dom',
      commonjs2: 'react-dom',
      amd: 'react-dom',
      root: 'ReactDOM'
    },
    '@phosphor-icons/react': '@phosphor-icons/react',
    '@radix-ui/react-dialog': '@radix-ui/react-dialog',
    '@radix-ui/react-popover': '@radix-ui/react-popover',
    '@radix-ui/react-select': '@radix-ui/react-select',
    '@radix-ui/react-tooltip': '@radix-ui/react-tooltip',
    'framer-motion': 'framer-motion',
    'date-fns': 'date-fns',
    'react-hook-form': 'react-hook-form',
    'react-slider': 'react-slider'
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      }
    ]
  },
  devtool: 'source-map'
};
