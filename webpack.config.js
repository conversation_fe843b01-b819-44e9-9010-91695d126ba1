const path = require('path');

module.exports = {
  mode: 'production',
  entry: './src/index.tsx', // ponto de entrada da sua biblioteca
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js', // bundle final
    library: '@ourtrip/ui', // nome da sua biblioteca
    libraryTarget: 'umd', // gera um bundle compatível com diversos ambientes (CommonJS, AMD, global)
    globalObject: 'this' // necessário para que funcione no Node.js e no browser
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js']
  },
  // Marque o React e o ReactDOM como "externals" para não incluí-los no bundle final
  externals: {
    react: 'react',
    'react-dom': 'react-dom'
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      }
    ]
  },
  devtool: 'source-map' // gera sourcemaps para facilitar o debug
};
