{"name": "@ourtrip/ui", "version": "1.0.17", "main": "dist/index.js", "module": "dist/index.modern.js", "source": "src/index.tsx", "types": "dist/types/index.d.ts", "scripts": {"build": "microbundle --jsx React.createElement --jsxFragment React.Fragment", "build:webpack": "webpack"}, "files": ["dist", "README.md", "LICENSE"], "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-slider": "^1.3.6", "microbundle": "^0.15.1", "react": "^19.0.0", "react-dom": "^19.0.0", "ts-loader": "^9.5.2", "typescript": "^5.8.2", "webpack": "^5.98.0", "webpack-cli": "^6.0.1"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {"@phosphor-icons/react": "^2.1.7", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/cli": "^4.0.13", "@tailwindcss/postcss": "^4.0.13", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "date-fns": "^4.1.0", "framer-motion": "^12.5.0", "postcss": "^8.5.3", "react-hook-form": "^7.54.2", "react-slider": "^2.0.6", "tailwindcss": "^4.0.13"}}