{
  "compilerOptions": {
    "target": "ES5",
    "module": "ESNext",
    "lib": ["dom", "es6", "es2017"],
    "jsx": "react",
    "declaration": true,
    "declarationDir": "./dist/types",
    "outDir": "./dist",
    "strict": true,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true
  },
  "include": ["src"],
}
