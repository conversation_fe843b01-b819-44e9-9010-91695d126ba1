# @ourtrip/ui

A modern React component library built with Tailwind CSS, providing a comprehensive set of UI components for building beautiful and responsive user interfaces.

## Installation

```bash
# Using npm
npm install @ourtrip/ui

# Using yarn
yarn add @ourtrip/ui

# Using pnpm
pnpm add @ourtrip/ui
```

## Setup

### Tailwind CSS Configuration

This component library requires Tailwind CSS. If you don't have it installed in your project, please follow the [official Tailwind CSS installation guide](https://tailwindcss.com/docs/installation).

Once Tailwind CSS is installed, update your `tailwind.config.js` or `tailwind.config.ts` file to include the following configuration:

```js
/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@ourtrip/ui/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
};

export default config;
```

This ensures Tailwind CSS processes the styles used in the @ourtrip/ui components.

## Usage

Import components from the library as follows:

```jsx
import { Button, Card, Input } from "@ourtrip/ui";

function App() {
  return (
    <Card>
      <h2>Hello World</h2>
      <Input label="Name" placeholder="Enter your name" />
      <Button>Submit</Button>
    </Card>
  );
}
```

## Components

### Accordion

Expandable content sections for organizing information.

```jsx
<Accordion title="Frequently Asked Questions" text="This is the content of the accordion" />
```

### Alert

Provides contextual feedback messages for user actions.

```jsx
<Alert type="success" variant="fill">
  Your data has been successfully saved!
</Alert>
```

**Props**:
- `type`: 'info' | 'success' | 'danger' | 'warning'
- `variant`: 'fill' | 'outline'

### Badge

Compact element to represent status, count, or categorization.

```jsx
<Badge type="success" size="normal">New</Badge>
```

**Props**:
- `type`: 'success' | 'warning' | 'danger' | 'info' | 'white' | 'primary' | 'secondary'
- `size`: 'small' | 'normal' | 'large'
- `icon`: ReactNode (optional)

### Breadcrumbs

Navigation element to show the user's location in the site hierarchy.

```jsx
const items = [
  { id: "1", text: "Home", href: "/" },
  { id: "2", text: "Products", href: "/products" },
  { id: "3", text: "Product Details", href: "/products/1" }
];

<Breadcrumbs items={items} />
```

### Button

Interactive element for user actions.

```jsx
<Button 
  variant="fill" 
  color="primary" 
  size="normal"
  onClick={() => console.log('Button clicked!')}
>
  Click Me
</Button>
```

**Props**:
- `variant`: 'fill' | 'outline'
- `color`: 'primary' | 'secondary' | 'danger' | 'gray' | 'white'
- `size`: 'small' | 'normal' | 'large' | 'icon'
- `loading`: boolean
- `disabled`: boolean
- `fullWidth`: boolean

### Card

Container for content with predefined styling.

```jsx
<Card>
  <h3>Card Title</h3>
  <p>Card content goes here</p>
</Card>
```

### Checkbox

Selection component for multiple choices.

```jsx
<Checkbox 
  label="I agree to terms and conditions" 
  onChange={(checked) => console.log(checked)} 
/>
```

**Props**:
- `value`: boolean
- `onChange`: (checked: boolean) => void
- `label`: ReactNode
- `trailing`: string | number (optional)

### Collapse

Toggleable content area.

```jsx
<Collapse 
  title="More Information" 
  content="This content can be hidden or shown"
  togglePosition="bottom" 
  showPreview={true}
/>
```

### Divider

Horizontal or vertical line to separate content.

```jsx
<Divider orientation="horizontal" />
```

**Props**:
- `orientation`: 'horizontal' | 'vertical'
- `fullHeight`: boolean

### Input

Text input field for user data entry.

```jsx
<Input 
  label="Email Address"
  placeholder="Enter your email"
  type="email"
  color="gray"
/>
```

**Props**:
- `label`: string
- `color`: 'white' | 'gray'
- `error`: string
- `disabled`: boolean

### Modal

Dialogs for important information or actions.

```jsx
const [isOpen, setIsOpen] = useState(false);

<>
  <Button onClick={() => setIsOpen(true)}>Open Modal</Button>
  <Modal 
    isOpen={isOpen} 
    onClose={() => setIsOpen(false)}
    showCloseButton={true}
  >
    <h3>Modal Title</h3>
    <p>Modal content</p>
  </Modal>
</>
```

### PhoneInput

Specialized input for phone numbers with country codes.

```jsx
<PhoneInput
  t={translation}
  DDI="+55"
  phoneDDIKey="phone.ddi"
  phoneAreaCodeKey="phone.areaCode"
  phoneNumberKey="phone.number"
  registerWithMask={registerWithMask}
  setFocus={setFocus}
  errors={errors}
/>
```

### Popover

Floating content that appears on interaction.

```jsx
<Popover>
  <PopoverTrigger asChild>
    <Button>Open Popover</Button>
  </PopoverTrigger>
  <PopoverContent>
    <p>Popover content</p>
  </PopoverContent>
</Popover>
```

### Radio

Selection component for single choices.

```jsx
<Radio label="Option 1" name="options" value="1" />
<Radio label="Option 2" name="options" value="2" />
```

**Props**:
- `label`: string
- `size`: 'small' | 'normal' | 'large'

### Range

Slider for selecting a value or range.

```jsx
<Range 
  min={0} 
  max={100} 
  initialValues={[20, 80]}
  onChange={(values) => console.log(values)}
/>
```

### Select

Dropdown selection component.

```jsx
<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select an option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>
```

### Sheet

Slide-in panel from the edge of the screen.

```jsx
<Sheet>
  <SheetTrigger asChild>
    <Button>Open Sheet</Button>
  </SheetTrigger>
  <SheetContent side="right">
    <h3>Sheet Title</h3>
    <p>Sheet content</p>
  </SheetContent>
</Sheet>
```

### Stars

Rating component for displaying ratings.

```jsx
<Stars rate={4.5} />
```

### StepMarker

Visual indicator for multi-step processes.

```jsx
<StepMarker 
  step={1} 
  title="Personal Information"
  subtitle="Enter your details"
  checked={false}
  active={true}
  onClick={() => goToStep(1)}
/>
```

### Switch

Toggle between two states.

```jsx
<Switch 
  on={isEnabled} 
  onChange={(on) => setIsEnabled(on)}
  label="Enable notifications"
/>
```

### Tag

Label element for categorization.

```jsx
<Tag color="primary" onClick={() => console.log('Tag clicked')}>
  Featured
</Tag>
```

**Props**:
- `color`: 'primary' | 'secondary' | 'white'
- `iconLeft`: ReactNode
- `iconRight`: ReactNode

### Tooltip

Information that appears on hover.

```jsx
<Tooltip text="Additional information" active={true}>
  <Button>Hover me</Button>
</Tooltip>
```

## Utility Functions

The library provides some utility functions:

### Validation

```jsx
import { isValidEmail, isValidCpf, isFullName } from '@ourtrip/ui';

const isEmail = isValidEmail('<EMAIL>'); // Returns true
```

Available validation functions:
- `isValidCpf`: Validates a Brazilian CPF
- `isValidCNPJ`: Validates a Brazilian CNPJ
- `isCPF`: Checks if a string is a valid CPF
- `isCNPJ`: Checks if a string is a valid CNPJ
- `isFullName`: Validates if a string is a full name
- `isValidName`: Validates if a string is a valid name
- `isValidEmail`: Validates email format
- `isValidPhoneNumber`: Validates phone number format
- `isValidNumberCode`: Validates a numeric code
- `isValidDDD`: Validates Brazilian area codes
- `isValidChildDate`: Validates if a date is for a minor
- `isValidAdult`: Validates if a date is for an adult

### Classes

```jsx
import { cn } from '@ourtrip/ui';

const className = cn('base-class', condition && 'conditional-class');
```

## Browser Support

This library supports modern browsers including:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

ISC

## Dependencies

This library builds on several excellent packages including:
- React and React DOM
- @phosphor-icons/react
- Radix UI components
- Tailwind CSS
- Framer Motion
- date-fns

You don't need to install these dependencies separately as they are included in the package.