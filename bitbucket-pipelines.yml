image: node:18

definitions:
  caches:
    npm: ~/.npm

pipelines:
  branches:
    master:
      - step:
          name: Build and publish to npm
          caches:
            - npm
          script:
            - npm install --legacy-peer-deps
            - npm run build
            - echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" > ~/.npmrc
            - npm publish --access public
          artifacts:
            - dist/**

  pull-requests:
    '**':
      - step:
          name: Build and test
          caches:
            - npm
          script:
            - npm install
            - npm run build