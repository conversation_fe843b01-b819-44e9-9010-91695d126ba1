# OurTrip UI Documentation

This is the documentation site for the OurTrip UI component library, built with Next.js and styled to match the shadcn/ui documentation experience.

## Features

- 📚 **Comprehensive Documentation** - Complete API reference for all components
- 🎮 **Interactive Playground** - Live component previews with customizable props
- 🎨 **Beautiful Design** - Clean, modern interface inspired by shadcn/ui
- 🌙 **Dark Mode** - Toggle between light and dark themes
- 📱 **Responsive** - Works perfectly on all device sizes
- 🔍 **Easy Navigation** - Organized sidebar with component categories
- 📋 **Copy Code** - One-click code copying for all examples

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <your-repo-url>
cd ui/docs
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Install the UI library:
```bash
npm install ../
```

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
docs/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── docs/              # Documentation pages
│   │   │   ├── installation/  # Installation guide
│   │   │   ├── usage/         # Usage examples
│   │   │   └── components/    # Component documentation
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # Documentation components
│   │   ├── component-preview.tsx    # Component preview with code
│   │   ├── component-playground.tsx # Interactive playground
│   │   ├── code-block.tsx          # Code display component
│   │   ├── header.tsx              # Site header
│   │   ├── sidebar.tsx             # Navigation sidebar
│   │   └── theme-provider.tsx      # Theme context
│   └── lib/
│       └── utils.ts           # Utility functions
├── package.json
├── next.config.js
├── tailwind.config.js
└── tsconfig.json
```

## Adding New Component Documentation

To add documentation for a new component:

1. Create a new page in `src/app/docs/components/[component-name]/page.tsx`
2. Add the component to the sidebar navigation in `src/components/sidebar.tsx`
3. Follow the existing pattern with sections for:
   - Import statement
   - Basic usage
   - Examples with variants
   - Interactive playground (optional)
   - API reference table

Example structure:
```tsx
import { YourComponent } from "@ourtrip/ui";
import { ComponentPreview } from "@/components/component-preview";
import { CodeBlock } from "@/components/code-block";

export default function YourComponentPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Your Component</h1>
        <p className="text-xl text-muted-foreground">
          Component description
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Import</h2>
        <CodeBlock code={`import { YourComponent } from "@ourtrip/ui";`} />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Usage</h2>
        <ComponentPreview code={`<YourComponent />`}>
          <YourComponent />
        </ComponentPreview>
      </section>

      {/* More sections... */}
    </div>
  );
}
```

## Customization

### Theming

The documentation site uses CSS variables for theming. You can customize colors in `src/app/globals.css`:

```css
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  /* ... more variables */
}
```

### Styling

The site uses Tailwind CSS. You can customize the design system in `tailwind.config.js`.

## Deployment

The documentation can be deployed to any platform that supports Next.js:

### Vercel (Recommended)
```bash
npm run build
```

### Other Platforms
```bash
npm run build
npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your documentation
4. Test locally
5. Submit a pull request

## License

This documentation is part of the OurTrip UI project and follows the same license.
