#!/bin/bash

echo "🚀 Starting OurTrip UI Documentation..."
echo ""

# Check if we're in the docs directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the docs directory"
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install --legacy-peer-deps
fi

# Start the development server
echo "🌟 Starting development server..."
echo "📖 Documentation will be available at: http://localhost:3000"
echo ""
npm run dev
