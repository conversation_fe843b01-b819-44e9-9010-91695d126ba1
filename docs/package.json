{"name": "@ourtrip/ui-docs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ourtrip/ui": "file:../", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.5.0", "lucide-react": "^0.460.0", "next": "15.1.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "rehype-highlight": "^7.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^4.0.13", "typescript": "^5"}}