/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/docs/installation/page";
exports.ids = ["app/docs/installation/page"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Finstallation%2Fpage&page=%2Fdocs%2Finstallation%2Fpage&appPaths=%2Fdocs%2Finstallation%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Finstallation%2Fpage.tsx&appDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Finstallation%2Fpage&page=%2Fdocs%2Finstallation%2Fpage&appPaths=%2Fdocs%2Finstallation%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Finstallation%2Fpage.tsx&appDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?91d2\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/docs/installation/page.tsx */ \"(rsc)/./src/app/docs/installation/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'docs',\n        {\n        children: [\n        'installation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/docs/installation/page\",\n        pathname: \"/docs/installation\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Finstallation%2Fpage&page=%2Fdocs%2Finstallation%2Fpage&appPaths=%2Fdocs%2Finstallation%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Finstallation%2Fpage.tsx&appDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fsidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fsidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/header.tsx */ \"(rsc)/./src/components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sidebar.tsx */ \"(rsc)/./src/components/sidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(rsc)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fsidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fsidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fsidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/header.tsx */ \"(ssr)/./src/components/header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sidebar.tsx */ \"(ssr)/./src/components/sidebar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/theme-provider.tsx */ \"(ssr)/./src/components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fheader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Fsidebar.tsx%22%2C%22ids%22%3A%5B%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fcomponents%2Ftheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fdocs%2Finstallation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fdocs%2Finstallation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/docs/installation/page.tsx */ \"(rsc)/./src/app/docs/installation/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZGFuaWVsJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGb3VydHJpcCUyRnVpJTJGZG9jcyUyRnNyYyUyRmFwcCUyRmRvY3MlMkZpbnN0YWxsYXRpb24lMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL3NyYy9hcHAvZG9jcy9pbnN0YWxsYXRpb24vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fdocs%2Finstallation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fdocs%2Finstallation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fdocs%2Finstallation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/docs/installation/page.tsx */ \"(ssr)/./src/app/docs/installation/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGZGFuaWVsJTJGRG9jdW1lbnRzJTJGR2l0SHViJTJGb3VydHJpcCUyRnVpJTJGZG9jcyUyRnNyYyUyRmFwcCUyRmRvY3MlMkZpbnN0YWxsYXRpb24lMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXNIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL3NyYy9hcHAvZG9jcy9pbnN0YWxsYXRpb24vcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp%2Fdocs%2Finstallation%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/docs/installation/page.tsx":
/*!********************************************!*\
  !*** ./src/app/docs/installation/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InstallationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_code_block__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/code-block */ \"(ssr)/./src/components/code-block.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction InstallationPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold tracking-tight\",\n                        children: \"Installation\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-muted-foreground\",\n                        children: \"Get started with OurTrip UI by installing the package and configuring your project.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold tracking-tight\",\n                                children: \"Install the package\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Install OurTrip UI using your preferred package manager:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                title: \"npm\",\n                                code: \"npm install @ourtrip/ui\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                title: \"yarn\",\n                                code: \"yarn add @ourtrip/ui\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                title: \"pnpm\",\n                                code: \"pnpm add @ourtrip/ui\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold tracking-tight\",\n                                children: \"Configure Tailwind CSS\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"OurTrip UI is built with Tailwind CSS. You need to install and configure Tailwind CSS in your project.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium\",\n                                children: \"Install Tailwind CSS\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                code: \"npm install -D tailwindcss postcss autoprefixer npx tailwindcss init -p\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium\",\n                                children: \"Configure your template paths\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: [\n                                    \"Add the paths to all of your template files in your \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-muted px-1 py-0.5 rounded\",\n                                        children: \"tailwind.config.js\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 65\n                                    }, this),\n                                    \" file:\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                title: \"tailwind.config.js\",\n                                language: \"javascript\",\n                                code: `/** @type {import('tailwindcss').Config} */\nmodule.exports = {\n  content: [\n    \"./src/**/*.{js,ts,jsx,tsx,mdx}\",\n    \"./node_modules/@ourtrip/ui/**/*.{js,ts,jsx,tsx}\"\n  ],\n  theme: {\n    extend: {},\n  },\n  plugins: [],\n}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium\",\n                                children: \"Add Tailwind directives\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Add the Tailwind directives to your CSS file:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                title: \"globals.css\",\n                                language: \"css\",\n                                code: `@tailwind base;\n@tailwind components;\n@tailwind utilities;`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold tracking-tight\",\n                                children: \"Start using components\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"You can now start using OurTrip UI components in your project:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_code_block__WEBPACK_IMPORTED_MODULE_1__.CodeBlock, {\n                                title: \"app.tsx\",\n                                code: `import { Button, Card, Input } from \"@ourtrip/ui\";\n\nfunction App() {\n  return (\n    <Card>\n      <h2>Hello World</h2>\n      <Input label=\"Name\" placeholder=\"Enter your name\" />\n      <Button>Submit</Button>\n    </Card>\n  );\n}`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/docs/installation/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/code-block.tsx":
/*!***************************************!*\
  !*** ./src/components/code-block.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeBlock: () => (/* binding */ CodeBlock)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* __next_internal_client_entry_do_not_use__ CodeBlock auto */ \n\n\nfunction CodeBlock({ code, language = \"tsx\", title }) {\n    const [copied, setCopied] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const copyToClipboard = async ()=>{\n        await navigator.clipboard.writeText(code);\n        setCopied(true);\n        setTimeout(()=>setCopied(false), 2000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between rounded-t-lg border border-b-0 bg-muted px-4 py-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        className: `bg-muted p-4 overflow-x-auto ${title ? 'rounded-t-none' : 'rounded-lg'}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"text-sm\",\n                            children: code\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"absolute right-4 top-4 h-8 w-8 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n                        onClick: copyToClipboard,\n                        children: [\n                            copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Copy\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/code-block.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/code-block.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/header.tsx":
/*!***********************************!*\
  !*** ./src/components/header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Github_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Github_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Github,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header() {\n    const { theme, setTheme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container flex h-14 items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mr-4 flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"mr-6 flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold text-xl\",\n                                children: \"OurTrip UI\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center space-x-6 text-sm font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/docs/installation\",\n                                    className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                                    children: \"Documentation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/docs/components/button\",\n                                    className: \"transition-colors hover:text-foreground/80 text-foreground/60\",\n                                    children: \"Components\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 items-center justify-between space-x-2 md:justify-end\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full flex-1 md:w-auto md:flex-none\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\",\n                                    onClick: ()=>setTheme(theme === \"light\" ? \"dark\" : \"light\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"Toggle theme\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://github.com/yourorg/ourtrip-ui\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Github_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-[1.2rem] w-[1.2rem]\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"sr-only\",\n                                            children: \"GitHub\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-scroll-area */ \"(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\nconst sidebarNavItems = [\n    {\n        title: \"Getting Started\",\n        items: [\n            {\n                title: \"Installation\",\n                href: \"/docs/installation\"\n            },\n            {\n                title: \"Usage\",\n                href: \"/docs/usage\"\n            }\n        ]\n    },\n    {\n        title: \"Components\",\n        items: [\n            {\n                title: \"Accordion\",\n                href: \"/docs/components/accordion\"\n            },\n            {\n                title: \"Alert\",\n                href: \"/docs/components/alert\"\n            },\n            {\n                title: \"Badge\",\n                href: \"/docs/components/badge\"\n            },\n            {\n                title: \"Breadcrumbs\",\n                href: \"/docs/components/breadcrumbs\"\n            },\n            {\n                title: \"Button\",\n                href: \"/docs/components/button\"\n            },\n            {\n                title: \"Card\",\n                href: \"/docs/components/card\"\n            },\n            {\n                title: \"Checkbox\",\n                href: \"/docs/components/checkbox\"\n            },\n            {\n                title: \"Collapse\",\n                href: \"/docs/components/collapse\"\n            },\n            {\n                title: \"Divider\",\n                href: \"/docs/components/divider\"\n            },\n            {\n                title: \"Input\",\n                href: \"/docs/components/input\"\n            },\n            {\n                title: \"Modal\",\n                href: \"/docs/components/modal\"\n            },\n            {\n                title: \"Pagination\",\n                href: \"/docs/components/pagination\"\n            },\n            {\n                title: \"Phone Input\",\n                href: \"/docs/components/phone\"\n            },\n            {\n                title: \"Popover\",\n                href: \"/docs/components/popover\"\n            },\n            {\n                title: \"Radio\",\n                href: \"/docs/components/radio\"\n            },\n            {\n                title: \"Range\",\n                href: \"/docs/components/range\"\n            },\n            {\n                title: \"Select\",\n                href: \"/docs/components/select\"\n            },\n            {\n                title: \"Sheet\",\n                href: \"/docs/components/sheet\"\n            },\n            {\n                title: \"Stars\",\n                href: \"/docs/components/stars\"\n            },\n            {\n                title: \"Step Marker\",\n                href: \"/docs/components/step-marker\"\n            },\n            {\n                title: \"Switch\",\n                href: \"/docs/components/switch\"\n            },\n            {\n                title: \"Tag\",\n                href: \"/docs/components/tag\"\n            },\n            {\n                title: \"Tooltip\",\n                href: \"/docs/components/tooltip\"\n            }\n        ]\n    }\n];\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-64 border-r bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n            className: \"h-[calc(100vh-3.5rem)] py-6 pl-8 pr-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: sidebarNavItems.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"mb-2 px-2 py-1 text-sm font-semibold tracking-tight\",\n                                children: section.title\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-1\",\n                                children: section.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"block select-none space-y-1 rounded-md p-2 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\", pathname === item.href ? \"bg-accent text-accent-foreground\" : \"text-muted-foreground\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm font-medium leading-none\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, section.title, true, {\n                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9zaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFNkI7QUFDaUI7QUFDYjtBQUN3QjtBQUV6RCxNQUFNSSxrQkFBa0I7SUFDdEI7UUFDRUMsT0FBTztRQUNQQyxPQUFPO1lBQ0w7Z0JBQ0VELE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7U0FDRDtJQUNIO0lBQ0E7UUFDRUYsT0FBTztRQUNQQyxPQUFPO1lBQ0w7Z0JBQ0VELE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7WUFDQTtnQkFDRUYsT0FBTztnQkFDUEUsTUFBTTtZQUNSO1lBQ0E7Z0JBQ0VGLE9BQU87Z0JBQ1BFLE1BQU07WUFDUjtZQUNBO2dCQUNFRixPQUFPO2dCQUNQRSxNQUFNO1lBQ1I7U0FDRDtJQUNIO0NBQ0Q7QUFFTSxTQUFTQztJQUNkLE1BQU1DLFdBQVdSLDREQUFXQTtJQUU1QixxQkFDRSw4REFBQ1M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ1IsbUVBQVVBO1lBQUNRLFdBQVU7c0JBQ3BCLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFDWlAsZ0JBQWdCUSxHQUFHLENBQUMsQ0FBQ0Msd0JBQ3BCLDhEQUFDSDs7MENBQ0MsOERBQUNJO2dDQUFHSCxXQUFVOzBDQUNYRSxRQUFRUixLQUFLOzs7Ozs7MENBRWhCLDhEQUFDSztnQ0FBSUMsV0FBVTswQ0FDWkUsUUFBUVAsS0FBSyxDQUFDTSxHQUFHLENBQUMsQ0FBQ0cscUJBQ2xCLDhEQUFDZixrREFBSUE7d0NBRUhPLE1BQU1RLEtBQUtSLElBQUk7d0NBQ2ZJLFdBQVdULDhDQUFFQSxDQUNYLGlNQUNBTyxhQUFhTSxLQUFLUixJQUFJLEdBQ2xCLHFDQUNBO2tEQUdOLDRFQUFDRzs0Q0FBSUMsV0FBVTtzREFDWkksS0FBS1YsS0FBSzs7Ozs7O3VDQVZSVSxLQUFLUixJQUFJOzs7Ozs7Ozs7Ozt1QkFQWk0sUUFBUVIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE0Qm5DIiwic291cmNlcyI6WyIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL3NyYy9jb21wb25lbnRzL3NpZGViYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5pbXBvcnQgeyBTY3JvbGxBcmVhIH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zY3JvbGwtYXJlYVwiO1xuXG5jb25zdCBzaWRlYmFyTmF2SXRlbXMgPSBbXG4gIHtcbiAgICB0aXRsZTogXCJHZXR0aW5nIFN0YXJ0ZWRcIixcbiAgICBpdGVtczogW1xuICAgICAge1xuICAgICAgICB0aXRsZTogXCJJbnN0YWxsYXRpb25cIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9pbnN0YWxsYXRpb25cIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIlVzYWdlXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvdXNhZ2VcIixcbiAgICAgIH0sXG4gICAgXSxcbiAgfSxcbiAge1xuICAgIHRpdGxlOiBcIkNvbXBvbmVudHNcIixcbiAgICBpdGVtczogW1xuICAgICAge1xuICAgICAgICB0aXRsZTogXCJBY2NvcmRpb25cIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL2FjY29yZGlvblwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiQWxlcnRcIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL2FsZXJ0XCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJCYWRnZVwiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvYmFkZ2VcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIkJyZWFkY3J1bWJzXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9icmVhZGNydW1ic1wiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiQnV0dG9uXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9idXR0b25cIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIkNhcmRcIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL2NhcmRcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIkNoZWNrYm94XCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9jaGVja2JveFwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiQ29sbGFwc2VcIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL2NvbGxhcHNlXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJEaXZpZGVyXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9kaXZpZGVyXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJJbnB1dFwiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvaW5wdXRcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIk1vZGFsXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9tb2RhbFwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiUGFnaW5hdGlvblwiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvcGFnaW5hdGlvblwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiUGhvbmUgSW5wdXRcIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL3Bob25lXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJQb3BvdmVyXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9wb3BvdmVyXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJSYWRpb1wiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvcmFkaW9cIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIlJhbmdlXCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9yYW5nZVwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiU2VsZWN0XCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9zZWxlY3RcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIlNoZWV0XCIsXG4gICAgICAgIGhyZWY6IFwiL2RvY3MvY29tcG9uZW50cy9zaGVldFwiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiU3RhcnNcIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL3N0YXJzXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJTdGVwIE1hcmtlclwiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvc3RlcC1tYXJrZXJcIixcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIHRpdGxlOiBcIlN3aXRjaFwiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvc3dpdGNoXCIsXG4gICAgICB9LFxuICAgICAge1xuICAgICAgICB0aXRsZTogXCJUYWdcIixcbiAgICAgICAgaHJlZjogXCIvZG9jcy9jb21wb25lbnRzL3RhZ1wiLFxuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgdGl0bGU6IFwiVG9vbHRpcFwiLFxuICAgICAgICBocmVmOiBcIi9kb2NzL2NvbXBvbmVudHMvdG9vbHRpcFwiLFxuICAgICAgfSxcbiAgICBdLFxuICB9LFxuXTtcblxuZXhwb3J0IGZ1bmN0aW9uIFNpZGViYXIoKSB7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidy02NCBib3JkZXItciBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICA8U2Nyb2xsQXJlYSBjbGFzc05hbWU9XCJoLVtjYWxjKDEwMHZoLTMuNXJlbSldIHB5LTYgcGwtOCBwci02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAge3NpZGViYXJOYXZJdGVtcy5tYXAoKHNlY3Rpb24pID0+IChcbiAgICAgICAgICAgIDxkaXYga2V5PXtzZWN0aW9uLnRpdGxlfT5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cIm1iLTIgcHgtMiBweS0xIHRleHQtc20gZm9udC1zZW1pYm9sZCB0cmFja2luZy10aWdodFwiPlxuICAgICAgICAgICAgICAgIHtzZWN0aW9uLnRpdGxlfVxuICAgICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgIHtzZWN0aW9uLml0ZW1zLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICAgICAgICAgICAgICBcImJsb2NrIHNlbGVjdC1ub25lIHNwYWNlLXktMSByb3VuZGVkLW1kIHAtMiBsZWFkaW5nLW5vbmUgbm8tdW5kZXJsaW5lIG91dGxpbmUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICAgICAgICAgICAgICAgIHBhdGhuYW1lID09PSBpdGVtLmhyZWZcbiAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1hY2NlbnQgdGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCJcbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L1Njcm9sbEFyZWE+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTGluayIsInVzZVBhdGhuYW1lIiwiY24iLCJTY3JvbGxBcmVhIiwic2lkZWJhck5hdkl0ZW1zIiwidGl0bGUiLCJpdGVtcyIsImhyZWYiLCJTaWRlYmFyIiwicGF0aG5hbWUiLCJkaXYiLCJjbGFzc05hbWUiLCJtYXAiLCJzZWN0aW9uIiwiaDQiLCJpdGVtIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/theme-provider.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4QjtBQUNtQztBQUcxRCxTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kYW5pZWwvRG9jdW1lbnRzL0dpdEh1Yi9vdXJ0cmlwL3VpL2RvY3Mvc3JjL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlciB9IGZyb20gXCJuZXh0LXRoZW1lc1wiXG5pbXBvcnQgeyB0eXBlIFRoZW1lUHJvdmlkZXJQcm9wcyB9IGZyb20gXCJuZXh0LXRoZW1lcy9kaXN0L3R5cGVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiwgLi4ucHJvcHMgfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIHJldHVybiA8TmV4dFRoZW1lc1Byb3ZpZGVyIHsuLi5wcm9wc30+e2NoaWxkcmVufTwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwicHJvcHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiL1VzZXJzL2RhbmllbC9Eb2N1bWVudHMvR2l0SHViL291cnRyaXAvdWkvZG9jcy9zcmMvbGliL3V0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5a3c26c3e2f1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1YTNjMjZjM2UyZjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/docs/installation/page.tsx":
/*!********************************************!*\
  !*** ./src/app/docs/installation/page.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/docs/installation/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./src/components/theme-provider.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sidebar */ \"(rsc)/./src/components/sidebar.tsx\");\n/* harmony import */ var _components_header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/header */ \"(rsc)/./src/components/header.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"OurTrip UI - Component Library Documentation\",\n    description: \"A modern React component library built with Tailwind CSS\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen bg-background\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_header__WEBPACK_IMPORTED_MODULE_4__.Header, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1 p-6 lg:p-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto max-w-4xl\",\n                                        children: children\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/app/layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/header.tsx":
/*!***********************************!*\
  !*** ./src/components/header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Sidebar: () => (/* binding */ Sidebar)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Sidebar = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/sidebar.tsx",
"Sidebar",
);

/***/ }),

/***/ "(rsc)/./src/components/theme-provider.tsx":
/*!*******************************************!*\
  !*** ./src/components/theme-provider.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/GitHub/ourtrip/ui/docs/src/components/theme-provider.tsx",
"ThemeProvider",
);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdocs%2Finstallation%2Fpage&page=%2Fdocs%2Finstallation%2Fpage&appPaths=%2Fdocs%2Finstallation%2Fpage&pagePath=private-next-app-dir%2Fdocs%2Finstallation%2Fpage.tsx&appDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fdaniel%2FDocuments%2FGitHub%2Fourtrip%2Fui%2Fdocs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();