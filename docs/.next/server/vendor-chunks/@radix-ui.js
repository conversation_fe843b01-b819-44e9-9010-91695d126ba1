"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvbnVtYmVyL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvbnVtYmVyL3NyYy9udW1iZXIudHNcbmZ1bmN0aW9uIGNsYW1wKHZhbHVlLCBbbWluLCBtYXhdKSB7XG4gIHJldHVybiBNYXRoLm1pbihtYXgsIE1hdGgubWF4KG1pbiwgdmFsdWUpKTtcbn1cbmV4cG9ydCB7XG4gIGNsYW1wXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL2NvcmUvcHJpbWl0aXZlL3NyYy9wcmltaXRpdmUudHN4XG5mdW5jdGlvbiBjb21wb3NlRXZlbnRIYW5kbGVycyhvcmlnaW5hbEV2ZW50SGFuZGxlciwgb3VyRXZlbnRIYW5kbGVyLCB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9IHRydWUgfSA9IHt9KSB7XG4gIHJldHVybiBmdW5jdGlvbiBoYW5kbGVFdmVudChldmVudCkge1xuICAgIG9yaWdpbmFsRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIGlmIChjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPT09IGZhbHNlIHx8ICFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICByZXR1cm4gb3VyRXZlbnRIYW5kbGVyPy4oZXZlbnQpO1xuICAgIH1cbiAgfTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VFdmVudEhhbmRsZXJzXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kYW5pZWwvRG9jdW1lbnRzL0dpdEh1Yi9vdXJ0cmlwL3VpL2RvY3Mvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1kaXJlY3Rpb24vZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvZGlyZWN0aW9uL3NyYy9kaXJlY3Rpb24udHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xudmFyIERpcmVjdGlvbkNvbnRleHQgPSBSZWFjdC5jcmVhdGVDb250ZXh0KHZvaWQgMCk7XG52YXIgRGlyZWN0aW9uUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgY29uc3QgeyBkaXIsIGNoaWxkcmVuIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goRGlyZWN0aW9uQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogZGlyLCBjaGlsZHJlbiB9KTtcbn07XG5mdW5jdGlvbiB1c2VEaXJlY3Rpb24obG9jYWxEaXIpIHtcbiAgY29uc3QgZ2xvYmFsRGlyID0gUmVhY3QudXNlQ29udGV4dChEaXJlY3Rpb25Db250ZXh0KTtcbiAgcmV0dXJuIGxvY2FsRGlyIHx8IGdsb2JhbERpciB8fCBcImx0clwiO1xufVxudmFyIFByb3ZpZGVyID0gRGlyZWN0aW9uUHJvdmlkZXI7XG5leHBvcnQge1xuICBEaXJlY3Rpb25Qcm92aWRlcixcbiAgUHJvdmlkZXIsXG4gIHVzZURpcmVjdGlvblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence,Root auto */ // src/presence.tsx\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"usePresence.useEffect\": ()=>{\n            const currentAnimationName = getAnimationName(stylesRef.current);\n            prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n        }\n    }[\"usePresence.useEffect\"], [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            const styles = stylesRef.current;\n            const wasPresent = prevPresentRef.current;\n            const hasPresentChanged = wasPresent !== present;\n            if (hasPresentChanged) {\n                const prevAnimationName = prevAnimationNameRef.current;\n                const currentAnimationName = getAnimationName(styles);\n                if (present) {\n                    send(\"MOUNT\");\n                } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                    send(\"UNMOUNT\");\n                } else {\n                    const isAnimating = prevAnimationName !== currentAnimationName;\n                    if (wasPresent && isAnimating) {\n                        send(\"ANIMATION_OUT\");\n                    } else {\n                        send(\"UNMOUNT\");\n                    }\n                }\n                prevPresentRef.current = present;\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)({\n        \"usePresence.useLayoutEffect\": ()=>{\n            if (node) {\n                let timeoutId;\n                const ownerWindow = node.ownerDocument.defaultView ?? window;\n                const handleAnimationEnd = {\n                    \"usePresence.useLayoutEffect.handleAnimationEnd\": (event)=>{\n                        const currentAnimationName = getAnimationName(stylesRef.current);\n                        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                        if (event.target === node && isCurrentAnimation) {\n                            send(\"ANIMATION_END\");\n                            if (!prevPresentRef.current) {\n                                const currentFillMode = node.style.animationFillMode;\n                                node.style.animationFillMode = \"forwards\";\n                                timeoutId = ownerWindow.setTimeout({\n                                    \"usePresence.useLayoutEffect.handleAnimationEnd\": ()=>{\n                                        if (node.style.animationFillMode === \"forwards\") {\n                                            node.style.animationFillMode = currentFillMode;\n                                        }\n                                    }\n                                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"]);\n                            }\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationEnd\"];\n                const handleAnimationStart = {\n                    \"usePresence.useLayoutEffect.handleAnimationStart\": (event)=>{\n                        if (event.target === node) {\n                            prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                        }\n                    }\n                }[\"usePresence.useLayoutEffect.handleAnimationStart\"];\n                node.addEventListener(\"animationstart\", handleAnimationStart);\n                node.addEventListener(\"animationcancel\", handleAnimationEnd);\n                node.addEventListener(\"animationend\", handleAnimationEnd);\n                return ({\n                    \"usePresence.useLayoutEffect\": ()=>{\n                        ownerWindow.clearTimeout(timeoutId);\n                        node.removeEventListener(\"animationstart\", handleAnimationStart);\n                        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                        node.removeEventListener(\"animationend\", handleAnimationEnd);\n                    }\n                })[\"usePresence.useLayoutEffect\"];\n            } else {\n                send(\"ANIMATION_END\");\n            }\n        }\n    }[\"usePresence.useLayoutEffect\"], [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"usePresence.useCallback\": (node2)=>{\n                stylesRef.current = node2 ? getComputedStyle(node2) : null;\n                setNode(node2);\n            }\n        }[\"usePresence.useCallback\"], [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\nvar Root = Presence;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDTztBQUNZO0FBQ1Y7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZ0VBQVUsY0FBYyxLQUFLO0FBQzVDLGVBQWUsNkNBQWdCO0FBQy9CLFlBQVksNkJBQTZCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFHLFNBQVMsc0NBQXNDO0FBQzdFLEdBQUc7QUFDSCxrQ0FBa0MsS0FBSztBQUN2QyxXQUFXO0FBQ1gsQ0FBQyxJQUFJO0FBQ0w7QUFDQSxjQUFjLGdEQUFrQjtBQUNoQztBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL2RhbmllbC9Eb2N1bWVudHMvR2l0SHViL291cnRyaXAvdWkvZG9jcy9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvcHJpbWl0aXZlLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQgeyBjcmVhdGVTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBOT0RFUyA9IFtcbiAgXCJhXCIsXG4gIFwiYnV0dG9uXCIsXG4gIFwiZGl2XCIsXG4gIFwiZm9ybVwiLFxuICBcImgyXCIsXG4gIFwiaDNcIixcbiAgXCJpbWdcIixcbiAgXCJpbnB1dFwiLFxuICBcImxhYmVsXCIsXG4gIFwibGlcIixcbiAgXCJuYXZcIixcbiAgXCJvbFwiLFxuICBcInBcIixcbiAgXCJzZWxlY3RcIixcbiAgXCJzcGFuXCIsXG4gIFwic3ZnXCIsXG4gIFwidWxcIlxuXTtcbnZhciBQcmltaXRpdmUgPSBOT0RFUy5yZWR1Y2UoKHByaW1pdGl2ZSwgbm9kZSkgPT4ge1xuICBjb25zdCBTbG90ID0gY3JlYXRlU2xvdChgUHJpbWl0aXZlLiR7bm9kZX1gKTtcbiAgY29uc3QgTm9kZSA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IGFzQ2hpbGQsIC4uLnByaW1pdGl2ZVByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBub2RlO1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICB3aW5kb3dbU3ltYm9sLmZvcihcInJhZGl4LXVpXCIpXSA9IHRydWU7XG4gICAgfVxuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KENvbXAsIHsgLi4ucHJpbWl0aXZlUHJvcHMsIHJlZjogZm9yd2FyZGVkUmVmIH0pO1xuICB9KTtcbiAgTm9kZS5kaXNwbGF5TmFtZSA9IGBQcmltaXRpdmUuJHtub2RlfWA7XG4gIHJldHVybiB7IC4uLnByaW1pdGl2ZSwgW25vZGVdOiBOb2RlIH07XG59LCB7fSk7XG5mdW5jdGlvbiBkaXNwYXRjaERpc2NyZXRlQ3VzdG9tRXZlbnQodGFyZ2V0LCBldmVudCkge1xuICBpZiAodGFyZ2V0KSBSZWFjdERPTS5mbHVzaFN5bmMoKCkgPT4gdGFyZ2V0LmRpc3BhdGNoRXZlbnQoZXZlbnQpKTtcbn1cbnZhciBSb290ID0gUHJpbWl0aXZlO1xuZXhwb3J0IHtcbiAgUHJpbWl0aXZlLFxuICBSb290LFxuICBkaXNwYXRjaERpc2NyZXRlQ3VzdG9tRXZlbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // src/scroll-area.tsx\n\n\n\n\n\n\n\n\n\n\n// src/use-state-machine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer({\n        \"useStateMachine.useReducer\": (state, event)=>{\n            const nextState = machine[state][event];\n            return nextState ?? state;\n        }\n    }[\"useStateMachine.useReducer\"], initialState);\n}\n// src/scroll-area.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollArea.useComposedRefs[composedRefs]\": (node)=>setScrollArea(node)\n    }[\"ScrollArea.useComposedRefs[composedRefs]\"]);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbar.useEffect\": ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n            return ({\n                \"ScrollAreaScrollbar.useEffect\": ()=>{\n                    isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n                }\n            })[\"ScrollAreaScrollbar.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbar.useEffect\"], [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n            const scrollArea = context.scrollArea;\n            let hideTimer = 0;\n            if (scrollArea) {\n                const handlePointerEnter = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        setVisible(true);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerEnter\"];\n                const handlePointerLeave = {\n                    \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>{\n                        hideTimer = window.setTimeout({\n                            \"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\": ()=>setVisible(false)\n                        }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"], context.scrollHideDelay);\n                    }\n                }[\"ScrollAreaScrollbarHover.useEffect.handlePointerLeave\"];\n                scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n                return ({\n                    \"ScrollAreaScrollbarHover.useEffect\": ()=>{\n                        window.clearTimeout(hideTimer);\n                        scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                        scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n                    }\n                })[\"ScrollAreaScrollbarHover.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarHover.useEffect\"], [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\": ()=>send(\"SCROLL_END\")\n    }[\"ScrollAreaScrollbarScroll.useDebounceCallback[debounceScrollEnd]\"], 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            if (state === \"idle\") {\n                const hideTimer = window.setTimeout({\n                    \"ScrollAreaScrollbarScroll.useEffect.hideTimer\": ()=>send(\"HIDE\")\n                }[\"ScrollAreaScrollbarScroll.useEffect.hideTimer\"], context.scrollHideDelay);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>window.clearTimeout(hideTimer)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarScroll.useEffect\": ()=>{\n            const viewport = context.viewport;\n            const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n            if (viewport) {\n                let prevScrollPos = viewport[scrollDirection];\n                const handleScroll = {\n                    \"ScrollAreaScrollbarScroll.useEffect.handleScroll\": ()=>{\n                        const scrollPos = viewport[scrollDirection];\n                        const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                        if (hasScrollInDirectionChanged) {\n                            send(\"SCROLL\");\n                            debounceScrollEnd();\n                        }\n                        prevScrollPos = scrollPos;\n                    }\n                }[\"ScrollAreaScrollbarScroll.useEffect.handleScroll\"];\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaScrollbarScroll.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaScrollbarScroll.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaScrollbarScroll.useEffect\"], [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback({\n        \"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\": ()=>{\n            if (context.viewport) {\n                const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n                const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n                setVisible(isHorizontal ? isOverflowX : isOverflowY);\n            }\n        }\n    }[\"ScrollAreaScrollbarAuto.useDebounceCallback[handleResize]\"], 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarX.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarX.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarY.useEffect\": ()=>{\n            if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n        }\n    }[\"ScrollAreaScrollbarY.useEffect\"], [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\": (node)=>setScrollbar(node)\n    }[\"ScrollAreaScrollbarImpl.useComposedRefs[composeRefs]\"]);\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaScrollbarImpl.useEffect\": ()=>{\n            const handleWheel = {\n                \"ScrollAreaScrollbarImpl.useEffect.handleWheel\": (event)=>{\n                    const element = event.target;\n                    const isScrollbarWheel = scrollbar?.contains(element);\n                    if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n                }\n            }[\"ScrollAreaScrollbarImpl.useEffect.handleWheel\"];\n            document.addEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n            return ({\n                \"ScrollAreaScrollbarImpl.useEffect\": ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                        passive: false\n                    })\n            })[\"ScrollAreaScrollbarImpl.useEffect\"];\n        }\n    }[\"ScrollAreaScrollbarImpl.useEffect\"], [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"ScrollAreaThumbImpl.useComposedRefs[composedRef]\": (node)=>scrollbarContext.onThumbChange(node)\n    }[\"ScrollAreaThumbImpl.useComposedRefs[composedRef]\"]);\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback({\n        \"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\": ()=>{\n            if (removeUnlinkedScrollListenerRef.current) {\n                removeUnlinkedScrollListenerRef.current();\n                removeUnlinkedScrollListenerRef.current = void 0;\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useDebounceCallback[debounceScrollEnd]\"], 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ScrollAreaThumbImpl.useEffect\": ()=>{\n            const viewport = scrollAreaContext.viewport;\n            if (viewport) {\n                const handleScroll = {\n                    \"ScrollAreaThumbImpl.useEffect.handleScroll\": ()=>{\n                        debounceScrollEnd();\n                        if (!removeUnlinkedScrollListenerRef.current) {\n                            const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                            removeUnlinkedScrollListenerRef.current = listener;\n                            onThumbPositionChange();\n                        }\n                    }\n                }[\"ScrollAreaThumbImpl.useEffect.handleScroll\"];\n                onThumbPositionChange();\n                viewport.addEventListener(\"scroll\", handleScroll);\n                return ({\n                    \"ScrollAreaThumbImpl.useEffect\": ()=>viewport.removeEventListener(\"scroll\", handleScroll)\n                })[\"ScrollAreaThumbImpl.useEffect\"];\n            }\n        }\n    }[\"ScrollAreaThumbImpl.useEffect\"], [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const height2 = context.scrollbarX?.offsetHeight || 0;\n            context.onCornerHeightChange(height2);\n            setHeight(height2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    useResizeObserver(context.scrollbarY, {\n        \"ScrollAreaCornerImpl.useResizeObserver\": ()=>{\n            const width2 = context.scrollbarY?.offsetWidth || 0;\n            context.onCornerWidthChange(width2);\n            setWidth(width2);\n        }\n    }[\"ScrollAreaCornerImpl.useResizeObserver\"]);\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useDebounceCallback.useEffect\": ()=>({\n                \"useDebounceCallback.useEffect\": ()=>window.clearTimeout(debounceTimerRef.current)\n            })[\"useDebounceCallback.useEffect\"]\n    }[\"useDebounceCallback.useEffect\"], []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"useDebounceCallback.useCallback\": ()=>{\n            window.clearTimeout(debounceTimerRef.current);\n            debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n        }\n    }[\"useDebounceCallback.useCallback\"], [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)({\n        \"useResizeObserver.useLayoutEffect\": ()=>{\n            let rAF = 0;\n            if (element) {\n                const resizeObserver = new ResizeObserver({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        cancelAnimationFrame(rAF);\n                        rAF = window.requestAnimationFrame(handleResize);\n                    }\n                }[\"useResizeObserver.useLayoutEffect\"]);\n                resizeObserver.observe(element);\n                return ({\n                    \"useResizeObserver.useLayoutEffect\": ()=>{\n                        window.cancelAnimationFrame(rAF);\n                        resizeObserver.unobserve(element);\n                    }\n                })[\"useResizeObserver.useLayoutEffect\"];\n            }\n        }\n    }[\"useResizeObserver.useLayoutEffect\"], [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXNsb3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUM0QjtBQUNJO0FBQy9EO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQiw2Q0FBZ0I7QUFDaEMsWUFBWSx5QkFBeUI7QUFDckMsMEJBQTBCLDJDQUFjO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLDJDQUFjLCtCQUErQiwyQ0FBYztBQUN6RSxpQkFBaUIsaURBQW9CO0FBQ3JDLFVBQVU7QUFDVjtBQUNBO0FBQ0EsT0FBTztBQUNQLDZCQUE2QixzREFBRyxjQUFjLDJDQUEyQyxpREFBb0IsZUFBZSwrQ0FBa0IsMENBQTBDO0FBQ3hMO0FBQ0EsMkJBQTJCLHNEQUFHLGNBQWMsMkNBQTJDO0FBQ3ZGLEdBQUc7QUFDSCx5QkFBeUIsVUFBVTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDZDQUFnQjtBQUNwQyxZQUFZLHlCQUF5QjtBQUNyQyxRQUFRLGlEQUFvQjtBQUM1QjtBQUNBO0FBQ0EsNEJBQTRCLDJDQUFjO0FBQzFDLG9DQUFvQyx5RUFBVztBQUMvQztBQUNBLGFBQWEsK0NBQWtCO0FBQy9CO0FBQ0EsV0FBVywyQ0FBYyx1QkFBdUIsMkNBQWM7QUFDOUQsR0FBRztBQUNILDZCQUE2QixVQUFVO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsVUFBVTtBQUNsQywyQkFBMkIsc0RBQUcsQ0FBQyx1REFBUyxJQUFJLFVBQVU7QUFDdEQ7QUFDQSw4QkFBOEIsVUFBVTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxpREFBb0I7QUFDN0I7QUFDQTtBQUNBLDBCQUEwQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQSxNQUFNO0FBQ04sa0NBQWtDO0FBQ2xDLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQU9FO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kYW5pZWwvRG9jdW1lbnRzL0dpdEh1Yi9vdXJ0cmlwL3VpL2RvY3Mvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1zbG90L2Rpc3QvaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9zbG90LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjb21wb3NlUmVmcyB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzXCI7XG5pbXBvcnQgeyBGcmFnbWVudCBhcyBGcmFnbWVudDIsIGpzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuLy8gQF9fTk9fU0lERV9FRkZFQ1RTX19cbmZ1bmN0aW9uIGNyZWF0ZVNsb3Qob3duZXJOYW1lKSB7XG4gIGNvbnN0IFNsb3RDbG9uZSA9IC8qIEBfX1BVUkVfXyAqLyBjcmVhdGVTbG90Q2xvbmUob3duZXJOYW1lKTtcbiAgY29uc3QgU2xvdDIgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBjaGlsZHJlbiwgLi4uc2xvdFByb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBjaGlsZHJlbkFycmF5ID0gUmVhY3QuQ2hpbGRyZW4udG9BcnJheShjaGlsZHJlbik7XG4gICAgY29uc3Qgc2xvdHRhYmxlID0gY2hpbGRyZW5BcnJheS5maW5kKGlzU2xvdHRhYmxlKTtcbiAgICBpZiAoc2xvdHRhYmxlKSB7XG4gICAgICBjb25zdCBuZXdFbGVtZW50ID0gc2xvdHRhYmxlLnByb3BzLmNoaWxkcmVuO1xuICAgICAgY29uc3QgbmV3Q2hpbGRyZW4gPSBjaGlsZHJlbkFycmF5Lm1hcCgoY2hpbGQpID0+IHtcbiAgICAgICAgaWYgKGNoaWxkID09PSBzbG90dGFibGUpIHtcbiAgICAgICAgICBpZiAoUmVhY3QuQ2hpbGRyZW4uY291bnQobmV3RWxlbWVudCkgPiAxKSByZXR1cm4gUmVhY3QuQ2hpbGRyZW4ub25seShudWxsKTtcbiAgICAgICAgICByZXR1cm4gUmVhY3QuaXNWYWxpZEVsZW1lbnQobmV3RWxlbWVudCkgPyBuZXdFbGVtZW50LnByb3BzLmNoaWxkcmVuIDogbnVsbDtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gY2hpbGQ7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goU2xvdENsb25lLCB7IC4uLnNsb3RQcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYsIGNoaWxkcmVuOiBSZWFjdC5pc1ZhbGlkRWxlbWVudChuZXdFbGVtZW50KSA/IFJlYWN0LmNsb25lRWxlbWVudChuZXdFbGVtZW50LCB2b2lkIDAsIG5ld0NoaWxkcmVuKSA6IG51bGwgfSk7XG4gICAgfVxuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KFNsb3RDbG9uZSwgeyAuLi5zbG90UHJvcHMsIHJlZjogZm9yd2FyZGVkUmVmLCBjaGlsZHJlbiB9KTtcbiAgfSk7XG4gIFNsb3QyLmRpc3BsYXlOYW1lID0gYCR7b3duZXJOYW1lfS5TbG90YDtcbiAgcmV0dXJuIFNsb3QyO1xufVxudmFyIFNsb3QgPSAvKiBAX19QVVJFX18gKi8gY3JlYXRlU2xvdChcIlNsb3RcIik7XG4vLyBAX19OT19TSURFX0VGRkVDVFNfX1xuZnVuY3Rpb24gY3JlYXRlU2xvdENsb25lKG93bmVyTmFtZSkge1xuICBjb25zdCBTbG90Q2xvbmUgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBjaGlsZHJlbiwgLi4uc2xvdFByb3BzIH0gPSBwcm9wcztcbiAgICBpZiAoUmVhY3QuaXNWYWxpZEVsZW1lbnQoY2hpbGRyZW4pKSB7XG4gICAgICBjb25zdCBjaGlsZHJlblJlZiA9IGdldEVsZW1lbnRSZWYoY2hpbGRyZW4pO1xuICAgICAgY29uc3QgcHJvcHMyID0gbWVyZ2VQcm9wcyhzbG90UHJvcHMsIGNoaWxkcmVuLnByb3BzKTtcbiAgICAgIGlmIChjaGlsZHJlbi50eXBlICE9PSBSZWFjdC5GcmFnbWVudCkge1xuICAgICAgICBwcm9wczIucmVmID0gZm9yd2FyZGVkUmVmID8gY29tcG9zZVJlZnMoZm9yd2FyZGVkUmVmLCBjaGlsZHJlblJlZikgOiBjaGlsZHJlblJlZjtcbiAgICAgIH1cbiAgICAgIHJldHVybiBSZWFjdC5jbG9uZUVsZW1lbnQoY2hpbGRyZW4sIHByb3BzMik7XG4gICAgfVxuICAgIHJldHVybiBSZWFjdC5DaGlsZHJlbi5jb3VudChjaGlsZHJlbikgPiAxID8gUmVhY3QuQ2hpbGRyZW4ub25seShudWxsKSA6IG51bGw7XG4gIH0pO1xuICBTbG90Q2xvbmUuZGlzcGxheU5hbWUgPSBgJHtvd25lck5hbWV9LlNsb3RDbG9uZWA7XG4gIHJldHVybiBTbG90Q2xvbmU7XG59XG52YXIgU0xPVFRBQkxFX0lERU5USUZJRVIgPSBTeW1ib2woXCJyYWRpeC5zbG90dGFibGVcIik7XG4vLyBAX19OT19TSURFX0VGRkVDVFNfX1xuZnVuY3Rpb24gY3JlYXRlU2xvdHRhYmxlKG93bmVyTmFtZSkge1xuICBjb25zdCBTbG90dGFibGUyID0gKHsgY2hpbGRyZW4gfSkgPT4ge1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KEZyYWdtZW50MiwgeyBjaGlsZHJlbiB9KTtcbiAgfTtcbiAgU2xvdHRhYmxlMi5kaXNwbGF5TmFtZSA9IGAke293bmVyTmFtZX0uU2xvdHRhYmxlYDtcbiAgU2xvdHRhYmxlMi5fX3JhZGl4SWQgPSBTTE9UVEFCTEVfSURFTlRJRklFUjtcbiAgcmV0dXJuIFNsb3R0YWJsZTI7XG59XG52YXIgU2xvdHRhYmxlID0gLyogQF9fUFVSRV9fICovIGNyZWF0ZVNsb3R0YWJsZShcIlNsb3R0YWJsZVwiKTtcbmZ1bmN0aW9uIGlzU2xvdHRhYmxlKGNoaWxkKSB7XG4gIHJldHVybiBSZWFjdC5pc1ZhbGlkRWxlbWVudChjaGlsZCkgJiYgdHlwZW9mIGNoaWxkLnR5cGUgPT09IFwiZnVuY3Rpb25cIiAmJiBcIl9fcmFkaXhJZFwiIGluIGNoaWxkLnR5cGUgJiYgY2hpbGQudHlwZS5fX3JhZGl4SWQgPT09IFNMT1RUQUJMRV9JREVOVElGSUVSO1xufVxuZnVuY3Rpb24gbWVyZ2VQcm9wcyhzbG90UHJvcHMsIGNoaWxkUHJvcHMpIHtcbiAgY29uc3Qgb3ZlcnJpZGVQcm9wcyA9IHsgLi4uY2hpbGRQcm9wcyB9O1xuICBmb3IgKGNvbnN0IHByb3BOYW1lIGluIGNoaWxkUHJvcHMpIHtcbiAgICBjb25zdCBzbG90UHJvcFZhbHVlID0gc2xvdFByb3BzW3Byb3BOYW1lXTtcbiAgICBjb25zdCBjaGlsZFByb3BWYWx1ZSA9IGNoaWxkUHJvcHNbcHJvcE5hbWVdO1xuICAgIGNvbnN0IGlzSGFuZGxlciA9IC9eb25bQS1aXS8udGVzdChwcm9wTmFtZSk7XG4gICAgaWYgKGlzSGFuZGxlcikge1xuICAgICAgaWYgKHNsb3RQcm9wVmFsdWUgJiYgY2hpbGRQcm9wVmFsdWUpIHtcbiAgICAgICAgb3ZlcnJpZGVQcm9wc1twcm9wTmFtZV0gPSAoLi4uYXJncykgPT4ge1xuICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGNoaWxkUHJvcFZhbHVlKC4uLmFyZ3MpO1xuICAgICAgICAgIHNsb3RQcm9wVmFsdWUoLi4uYXJncyk7XG4gICAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICAgICAgfTtcbiAgICAgIH0gZWxzZSBpZiAoc2xvdFByb3BWYWx1ZSkge1xuICAgICAgICBvdmVycmlkZVByb3BzW3Byb3BOYW1lXSA9IHNsb3RQcm9wVmFsdWU7XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChwcm9wTmFtZSA9PT0gXCJzdHlsZVwiKSB7XG4gICAgICBvdmVycmlkZVByb3BzW3Byb3BOYW1lXSA9IHsgLi4uc2xvdFByb3BWYWx1ZSwgLi4uY2hpbGRQcm9wVmFsdWUgfTtcbiAgICB9IGVsc2UgaWYgKHByb3BOYW1lID09PSBcImNsYXNzTmFtZVwiKSB7XG4gICAgICBvdmVycmlkZVByb3BzW3Byb3BOYW1lXSA9IFtzbG90UHJvcFZhbHVlLCBjaGlsZFByb3BWYWx1ZV0uZmlsdGVyKEJvb2xlYW4pLmpvaW4oXCIgXCIpO1xuICAgIH1cbiAgfVxuICByZXR1cm4geyAuLi5zbG90UHJvcHMsIC4uLm92ZXJyaWRlUHJvcHMgfTtcbn1cbmZ1bmN0aW9uIGdldEVsZW1lbnRSZWYoZWxlbWVudCkge1xuICBsZXQgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlbGVtZW50LnByb3BzLCBcInJlZlwiKT8uZ2V0O1xuICBsZXQgbWF5V2FybiA9IGdldHRlciAmJiBcImlzUmVhY3RXYXJuaW5nXCIgaW4gZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZztcbiAgaWYgKG1heVdhcm4pIHtcbiAgICByZXR1cm4gZWxlbWVudC5yZWY7XG4gIH1cbiAgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihlbGVtZW50LCBcInJlZlwiKT8uZ2V0O1xuICBtYXlXYXJuID0gZ2V0dGVyICYmIFwiaXNSZWFjdFdhcm5pbmdcIiBpbiBnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nO1xuICBpZiAobWF5V2Fybikge1xuICAgIHJldHVybiBlbGVtZW50LnByb3BzLnJlZjtcbiAgfVxuICByZXR1cm4gZWxlbWVudC5wcm9wcy5yZWYgfHwgZWxlbWVudC5yZWY7XG59XG5leHBvcnQge1xuICBTbG90IGFzIFJvb3QsXG4gIFNsb3QsXG4gIFNsb3R0YWJsZSxcbiAgY3JlYXRlU2xvdCxcbiAgY3JlYXRlU2xvdHRhYmxlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyIvVXNlcnMvZGFuaWVsL0RvY3VtZW50cy9HaXRIdWIvb3VydHJpcC91aS9kb2NzL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtY2FsbGJhY2stcmVmL3NyYy91c2UtY2FsbGJhY2stcmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsOENBQThDLGtEQUFxQjtBQUNuRTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9kYW5pZWwvRG9jdW1lbnRzL0dpdEh1Yi9vdXJ0cmlwL3VpL2RvY3Mvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtbGF5b3V0LWVmZmVjdC9zcmMvdXNlLWxheW91dC1lZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gZ2xvYmFsVGhpcz8uZG9jdW1lbnQgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ })

};
;