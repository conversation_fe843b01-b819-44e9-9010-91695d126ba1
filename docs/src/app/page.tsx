import Link from "next/link";

export default function Home() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">
          OurTrip UI Documentation
        </h1>
        <p className="text-xl text-muted-foreground">
          A modern React component library built with Tailwind CSS, providing a comprehensive set of UI components for building beautiful and responsive user interfaces.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="p-6 rounded-lg border bg-card">
          <h2 className="text-2xl font-semibold mb-4">Getting Started</h2>
          <p className="text-muted-foreground mb-4">
            Learn how to install and configure OurTrip UI in your project.
          </p>
          <Link
            href="/docs/installation"
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Get Started
          </Link>
        </div>

        <div className="p-6 rounded-lg border bg-card">
          <h2 className="text-2xl font-semibold mb-4">Components</h2>
          <p className="text-muted-foreground mb-4">
            Explore our collection of reusable components with live examples.
          </p>
          <Link
            href="/docs/components/button"
            className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
          >
            Browse Components
          </Link>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Features</h2>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <h3 className="font-medium">🎨 Tailwind CSS</h3>
            <p className="text-sm text-muted-foreground">
              Built with Tailwind CSS for easy customization and consistent design.
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">♿ Accessible</h3>
            <p className="text-sm text-muted-foreground">
              Components follow accessibility best practices and ARIA guidelines.
            </p>
          </div>
          <div className="space-y-2">
            <h3 className="font-medium">🔧 TypeScript</h3>
            <p className="text-sm text-muted-foreground">
              Full TypeScript support with comprehensive type definitions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
