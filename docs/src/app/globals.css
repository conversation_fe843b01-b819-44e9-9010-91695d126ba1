@import "tailwindcss";

@theme {
  --color-background: #ffffff;
  --color-foreground: #0f172a;
  --color-card: #ffffff;
  --color-card-foreground: #0f172a;
  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;
  --color-primary: #3b82f6;
  --color-primary-foreground: #f8fafc;
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;
  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;
  --color-accent: #f1f5f9;
  --color-accent-foreground: #0f172a;
  --color-destructive: #ef4444;
  --color-destructive-foreground: #f8fafc;
  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #3b82f6;
  --radius: 0.5rem;

  /* Primary colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Secondary colors */
  --color-secondary-50: #fdf2f8;
  --color-secondary-100: #fce7f3;
  --color-secondary-200: #fbcfe8;
  --color-secondary-300: #f9a8d4;
  --color-secondary-400: #f472b6;
  --color-secondary-500: #ec4899;
  --color-secondary-600: #db2777;
  --color-secondary-700: #be185d;
  --color-secondary-800: #9d174d;

  /* Border radius */
  --radius-inner: 8px;
  --radius-default: 12px;
}

@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: #0f172a;
    --color-foreground: #f8fafc;
    --color-card: #0f172a;
    --color-card-foreground: #f8fafc;
    --color-popover: #0f172a;
    --color-popover-foreground: #f8fafc;
    --color-primary: #60a5fa;
    --color-primary-foreground: #0f172a;
    --color-secondary: #1e293b;
    --color-secondary-foreground: #f8fafc;
    --color-muted: #1e293b;
    --color-muted-foreground: #94a3b8;
    --color-accent: #1e293b;
    --color-accent-foreground: #f8fafc;
    --color-destructive: #dc2626;
    --color-destructive-foreground: #f8fafc;
    --color-border: #1e293b;
    --color-input: #1e293b;
    --color-ring: #93c5fd;
  }
}

.dark {
  --color-background: #0f172a;
  --color-foreground: #f8fafc;
  --color-card: #0f172a;
  --color-card-foreground: #f8fafc;
  --color-popover: #0f172a;
  --color-popover-foreground: #f8fafc;
  --color-primary: #60a5fa;
  --color-primary-foreground: #0f172a;
  --color-secondary: #1e293b;
  --color-secondary-foreground: #f8fafc;
  --color-muted: #1e293b;
  --color-muted-foreground: #94a3b8;
  --color-accent: #1e293b;
  --color-accent-foreground: #f8fafc;
  --color-destructive: #dc2626;
  --color-destructive-foreground: #f8fafc;
  --color-border: #1e293b;
  --color-input: #1e293b;
  --color-ring: #93c5fd;
}

/* Base styles */
* {
  border-color: var(--color-border);
}

body {
  background-color: var(--color-background);
  color: var(--color-foreground);
}

/* Code highlighting */
.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
  background: #f8f8f8;
  color: #333;
}

.dark .hljs {
  background: #1e1e1e;
  color: #d4d4d4;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-literal,
.hljs-section,
.hljs-link {
  color: #0000ff;
}

.dark .hljs-keyword,
.dark .hljs-selector-tag,
.dark .hljs-literal,
.dark .hljs-section,
.dark .hljs-link {
  color: #569cd6;
}

.hljs-string {
  color: #a31515;
}

.dark .hljs-string {
  color: #ce9178;
}

.hljs-comment {
  color: #008000;
}

.dark .hljs-comment {
  color: #6a9955;
}

.hljs-number {
  color: #09885a;
}

.dark .hljs-number {
  color: #b5cea8;
}

.hljs-tag {
  color: #800000;
}

.dark .hljs-tag {
  color: #569cd6;
}

.hljs-attr {
  color: #ff0000;
}

.dark .hljs-attr {
  color: #92c5f8;
}
