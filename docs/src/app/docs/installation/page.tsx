"use client";

import { CodeBlock } from "@/components/code-block";

export default function InstallationPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Installation</h1>
        <p className="text-xl text-muted-foreground">
          Get started with OurTrip UI by installing the package and configuring your project.
        </p>
      </div>

      <div className="space-y-6">
        <section className="space-y-4">
          <h2 className="text-2xl font-semibold tracking-tight">Install the package</h2>
          <p className="text-muted-foreground">
            Install OurTrip UI using your preferred package manager:
          </p>
          <CodeBlock
            title="npm"
            code="npm install @ourtrip/ui"
          />
          <CodeBlock
            title="yarn"
            code="yarn add @ourtrip/ui"
          />
          <CodeBlock
            title="pnpm"
            code="pnpm add @ourtrip/ui"
          />
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold tracking-tight">Configure Tailwind CSS</h2>
          <p className="text-muted-foreground">
            OurTrip UI is built with Tailwind CSS. You need to install and configure Tailwind CSS in your project.
          </p>
          
          <h3 className="text-lg font-medium">Install Tailwind CSS</h3>
          <CodeBlock
            code="npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p"
          />

          <h3 className="text-lg font-medium">Configure your template paths</h3>
          <p className="text-muted-foreground">
            Add the paths to all of your template files in your <code className="bg-muted px-1 py-0.5 rounded">tailwind.config.js</code> file:
          </p>
          <CodeBlock
            title="tailwind.config.js"
            language="javascript"
            code={`/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@ourtrip/ui/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}`}
          />

          <h3 className="text-lg font-medium">Add Tailwind directives</h3>
          <p className="text-muted-foreground">
            Add the Tailwind directives to your CSS file:
          </p>
          <CodeBlock
            title="globals.css"
            language="css"
            code={`@tailwind base;
@tailwind components;
@tailwind utilities;`}
          />
        </section>

        <section className="space-y-4">
          <h2 className="text-2xl font-semibold tracking-tight">Start using components</h2>
          <p className="text-muted-foreground">
            You can now start using OurTrip UI components in your project:
          </p>
          <CodeBlock
            title="app.tsx"
            code={`import { Button, Card, Input } from "@ourtrip/ui";

function App() {
  return (
    <Card>
      <h2>Hello World</h2>
      <Input label="Name" placeholder="Enter your name" />
      <Button>Submit</Button>
    </Card>
  );
}`}
          />
        </section>
      </div>
    </div>
  );
}
