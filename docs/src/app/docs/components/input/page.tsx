"use client";

import { Input, But<PERSON> } from "@ourtrip/ui";
import { ComponentPreview } from "@/components/component-preview";
import { CodeBlock } from "@/components/code-block";

export default function InputPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Input</h1>
        <p className="text-xl text-muted-foreground">
          A flexible input component for collecting user data with various configurations.
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Import</h2>
        <CodeBlock code={`import { Input } from "@ourtrip/ui";`} />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Usage</h2>
        <ComponentPreview
          code={`<Input placeholder="Enter your name" />`}
        >
          <Input placeholder="Enter your name" />
        </ComponentPreview>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Examples</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">With Label</h3>
            <ComponentPreview
              code={`<Input label="Full Name" placeholder="Enter your full name" />`}
            >
              <Input label="Full Name" placeholder="Enter your full name" />
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Colors</h3>
            <ComponentPreview
              code={`<div className="space-y-4">
  <Input label="White Background" color="white" placeholder="White input" />
  <Input label="Gray Background" color="gray" placeholder="Gray input" />
</div>`}
            >
              <div className="space-y-4 w-full">
                <Input label="White Background" color="white" placeholder="White input" />
                <Input label="Gray Background" color="gray" placeholder="Gray input" />
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">With Error</h3>
            <ComponentPreview
              code={`<Input 
  label="Email" 
  placeholder="Enter your email" 
  error="Please enter a valid email address"
/>`}
            >
              <Input 
                label="Email" 
                placeholder="Enter your email" 
                error="Please enter a valid email address"
              />
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Disabled State</h3>
            <ComponentPreview
              code={`<Input 
  label="Disabled Input" 
  placeholder="This input is disabled" 
  disabled 
/>`}
            >
              <Input 
                label="Disabled Input" 
                placeholder="This input is disabled" 
                disabled 
              />
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Different Input Types</h3>
            <ComponentPreview
              code={`<div className="space-y-4">
  <Input label="Email" type="email" placeholder="Enter your email" />
  <Input label="Password" type="password" placeholder="Enter your password" />
  <Input label="Number" type="number" placeholder="Enter a number" />
  <Input label="Date" type="date" />
</div>`}
            >
              <div className="space-y-4 w-full">
                <Input label="Email" type="email" placeholder="Enter your email" />
                <Input label="Password" type="password" placeholder="Enter your password" />
                <Input label="Number" type="number" placeholder="Enter a number" />
                <Input label="Date" type="date" />
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Form Example</h3>
            <ComponentPreview
              code={`<form className="space-y-4">
  <Input label="First Name" placeholder="Enter your first name" />
  <Input label="Last Name" placeholder="Enter your last name" />
  <Input label="Email" type="email" placeholder="Enter your email" />
  <Button type="submit" fullWidth>Submit</Button>
</form>`}
            >
              <form className="space-y-4 w-full">
                <Input label="First Name" placeholder="Enter your first name" />
                <Input label="Last Name" placeholder="Enter your last name" />
                <Input label="Email" type="email" placeholder="Enter your email" />
                <Button type="submit" fullWidth>Submit</Button>
              </form>
            </ComponentPreview>
          </div>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">API Reference</h2>
        <div className="rounded-lg border">
          <div className="p-6">
            <h3 className="text-lg font-medium mb-4">Props</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-medium">Prop</th>
                    <th className="text-left p-2 font-medium">Type</th>
                    <th className="text-left p-2 font-medium">Default</th>
                    <th className="text-left p-2 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody className="text-muted-foreground">
                  <tr className="border-b">
                    <td className="p-2 font-mono">label</td>
                    <td className="p-2">string</td>
                    <td className="p-2">-</td>
                    <td className="p-2">Label text for the input</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">color</td>
                    <td className="p-2">"white" | "gray"</td>
                    <td className="p-2">"white"</td>
                    <td className="p-2">Background color of the input</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">error</td>
                    <td className="p-2">string</td>
                    <td className="p-2">-</td>
                    <td className="p-2">Error message to display</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">dense</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Compact input styling</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">floating</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Floating label style</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">border</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Show border around input</td>
                  </tr>
                  <tr>
                    <td className="p-2 font-mono">disabled</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Disable the input</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              The Input component also accepts all standard HTML input attributes.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
