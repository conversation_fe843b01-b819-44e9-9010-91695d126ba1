import { But<PERSON> } from "@ourtrip/ui";
import { ComponentPreview } from "@/components/component-preview";
import { ComponentPlayground } from "@/components/component-playground";
import { CodeBlock } from "@/components/code-block";

export default function ButtonPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Button</h1>
        <p className="text-xl text-muted-foreground">
          A versatile button component with multiple variants, sizes, and states.
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Import</h2>
        <CodeBlock code={`import { Button } from "@ourtrip/ui";`} />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Usage</h2>
        <ComponentPreview
          code={`<Button>Click me</Button>`}
        >
          <Button>Click me</Button>
        </ComponentPreview>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Examples</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Variants</h3>
            <ComponentPreview
              code={`<div className="flex gap-4">
  <Button variant="fill">Fill</Button>
  <Button variant="outline">Outline</Button>
</div>`}
            >
              <div className="flex gap-4">
                <Button variant="fill">Fill</Button>
                <Button variant="outline">Outline</Button>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Colors</h3>
            <ComponentPreview
              code={`<div className="flex gap-4 flex-wrap">
  <Button color="primary">Primary</Button>
  <Button color="secondary">Secondary</Button>
  <Button color="danger">Danger</Button>
  <Button color="gray">Gray</Button>
  <Button color="white">White</Button>
</div>`}
            >
              <div className="flex gap-4 flex-wrap">
                <Button color="primary">Primary</Button>
                <Button color="secondary">Secondary</Button>
                <Button color="danger">Danger</Button>
                <Button color="gray">Gray</Button>
                <Button color="white">White</Button>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Sizes</h3>
            <ComponentPreview
              code={`<div className="flex gap-4 items-center">
  <Button size="small">Small</Button>
  <Button size="normal">Normal</Button>
  <Button size="large">Large</Button>
  <Button size="icon">🚀</Button>
</div>`}
            >
              <div className="flex gap-4 items-center">
                <Button size="small">Small</Button>
                <Button size="normal">Normal</Button>
                <Button size="large">Large</Button>
                <Button size="icon">🚀</Button>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Shapes</h3>
            <ComponentPreview
              code={`<div className="flex gap-4">
  <Button shape="default">Default</Button>
  <Button shape="rounded">Rounded</Button>
</div>`}
            >
              <div className="flex gap-4">
                <Button shape="default">Default</Button>
                <Button shape="rounded">Rounded</Button>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">States</h3>
            <ComponentPreview
              code={`<div className="flex gap-4">
  <Button>Normal</Button>
  <Button loading>Loading</Button>
  <Button disabled>Disabled</Button>
</div>`}
            >
              <div className="flex gap-4">
                <Button>Normal</Button>
                <Button loading>Loading</Button>
                <Button disabled>Disabled</Button>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Full Width</h3>
            <ComponentPreview
              code={`<Button fullWidth>Full Width Button</Button>`}
            >
              <Button fullWidth>Full Width Button</Button>
            </ComponentPreview>
          </div>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Interactive Playground</h2>
        <ComponentPlayground
          component={Button}
          controls={[
            {
              name: "variant",
              type: "select",
              options: ["fill", "outline"],
              defaultValue: "fill",
              label: "Variant",
            },
            {
              name: "color",
              type: "select",
              options: ["primary", "secondary", "danger", "gray", "white"],
              defaultValue: "primary",
              label: "Color",
            },
            {
              name: "size",
              type: "select",
              options: ["small", "normal", "large", "icon"],
              defaultValue: "normal",
              label: "Size",
            },
            {
              name: "shape",
              type: "select",
              options: ["default", "rounded"],
              defaultValue: "default",
              label: "Shape",
            },
            {
              name: "loading",
              type: "boolean",
              defaultValue: false,
              label: "Loading",
            },
            {
              name: "disabled",
              type: "boolean",
              defaultValue: false,
              label: "Disabled",
            },
            {
              name: "fullWidth",
              type: "boolean",
              defaultValue: false,
              label: "Full Width",
            },
          ]}
        >
          Click me
        </ComponentPlayground>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">API Reference</h2>
        <div className="rounded-lg border">
          <div className="p-6">
            <h3 className="text-lg font-medium mb-4">Props</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-medium">Prop</th>
                    <th className="text-left p-2 font-medium">Type</th>
                    <th className="text-left p-2 font-medium">Default</th>
                    <th className="text-left p-2 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody className="text-muted-foreground">
                  <tr className="border-b">
                    <td className="p-2 font-mono">variant</td>
                    <td className="p-2">"fill" | "outline"</td>
                    <td className="p-2">"fill"</td>
                    <td className="p-2">The visual style variant</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">color</td>
                    <td className="p-2">"primary" | "secondary" | "danger" | "gray" | "white"</td>
                    <td className="p-2">"primary"</td>
                    <td className="p-2">The color theme</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">size</td>
                    <td className="p-2">"small" | "normal" | "large" | "icon"</td>
                    <td className="p-2">"normal"</td>
                    <td className="p-2">The size of the button</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">shape</td>
                    <td className="p-2">"default" | "rounded"</td>
                    <td className="p-2">"default"</td>
                    <td className="p-2">The border radius style</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">loading</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Shows loading spinner</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">disabled</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Disables the button</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">fullWidth</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Makes button full width</td>
                  </tr>
                  <tr>
                    <td className="p-2 font-mono">fullHeight</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Makes button full height</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
