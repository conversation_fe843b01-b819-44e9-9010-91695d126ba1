"use client";

import { ComponentPreview } from "@/components/component-preview";
import { ComponentPlayground } from "@/components/component-playground";
import { CodeBlock } from "@/components/code-block";
import * as React from "react";

// Simple Button component for demo
function SimpleButton({ children, className = "", ...props }: any) {
  return (
    <button
      className={`inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

export default function ButtonPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Button</h1>
        <p className="text-xl text-muted-foreground">
          A versatile button component with multiple variants, sizes, and states.
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Import</h2>
        <CodeBlock code={`import { Button } from "@ourtrip/ui";`} />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Usage</h2>
        <ComponentPreview
          code={`<Button>Click me</Button>`}
        >
          <SimpleButton>Click me</SimpleButton>
        </ComponentPreview>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Examples</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Variants</h3>
            <ComponentPreview
              code={`<div className="flex gap-4">
  <Button variant="fill">Fill</Button>
  <Button variant="outline">Outline</Button>
</div>`}
            >
              <div className="flex gap-4">
                <SimpleButton>Fill</SimpleButton>
                <SimpleButton className="border border-input bg-background hover:bg-accent hover:text-accent-foreground">Outline</SimpleButton>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Colors</h3>
            <ComponentPreview
              code={`<div className="flex gap-4 flex-wrap">
  <Button color="primary">Primary</Button>
  <Button color="secondary">Secondary</Button>
  <Button color="danger">Danger</Button>
  <Button color="gray">Gray</Button>
  <Button color="white">White</Button>
</div>`}
            >
              <div className="flex gap-4 flex-wrap">
                <SimpleButton>Primary</SimpleButton>
                <SimpleButton className="bg-secondary text-secondary-foreground hover:bg-secondary/90">Secondary</SimpleButton>
                <SimpleButton className="bg-destructive text-destructive-foreground hover:bg-destructive/90">Danger</SimpleButton>
                <SimpleButton className="bg-muted text-muted-foreground hover:bg-muted/90">Gray</SimpleButton>
                <SimpleButton className="bg-white text-black hover:bg-gray-100 border">White</SimpleButton>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Sizes</h3>
            <ComponentPreview
              code={`<div className="flex gap-4 items-center">
  <Button size="small">Small</Button>
  <Button size="normal">Normal</Button>
  <Button size="large">Large</Button>
  <Button size="icon">🚀</Button>
</div>`}
            >
              <div className="flex gap-4 items-center">
                <SimpleButton className="h-8 px-3 text-sm">Small</SimpleButton>
                <SimpleButton>Normal</SimpleButton>
                <SimpleButton className="h-12 px-8 text-lg">Large</SimpleButton>
                <SimpleButton className="h-10 w-10 p-0">🚀</SimpleButton>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Shapes</h3>
            <ComponentPreview
              code={`<div className="flex gap-4">
  <Button shape="default">Default</Button>
  <Button shape="rounded">Rounded</Button>
</div>`}
            >
              <div className="flex gap-4">
                <SimpleButton>Default</SimpleButton>
                <SimpleButton className="rounded-full">Rounded</SimpleButton>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">States</h3>
            <ComponentPreview
              code={`<div className="flex gap-4">
  <Button>Normal</Button>
  <Button loading>Loading</Button>
  <Button disabled>Disabled</Button>
</div>`}
            >
              <div className="flex gap-4">
                <SimpleButton>Normal</SimpleButton>
                <SimpleButton>Loading...</SimpleButton>
                <SimpleButton disabled className="opacity-50 cursor-not-allowed">Disabled</SimpleButton>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Full Width</h3>
            <ComponentPreview
              code={`<Button fullWidth>Full Width Button</Button>`}
            >
              <SimpleButton className="w-full">Full Width Button</SimpleButton>
            </ComponentPreview>
          </div>
        </div>
      </section>



      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">API Reference</h2>
        <div className="rounded-lg border">
          <div className="p-6">
            <h3 className="text-lg font-medium mb-4">Props</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-medium">Prop</th>
                    <th className="text-left p-2 font-medium">Type</th>
                    <th className="text-left p-2 font-medium">Default</th>
                    <th className="text-left p-2 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody className="text-muted-foreground">
                  <tr className="border-b">
                    <td className="p-2 font-mono">variant</td>
                    <td className="p-2">"fill" | "outline"</td>
                    <td className="p-2">"fill"</td>
                    <td className="p-2">The visual style variant</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">color</td>
                    <td className="p-2">"primary" | "secondary" | "danger" | "gray" | "white"</td>
                    <td className="p-2">"primary"</td>
                    <td className="p-2">The color theme</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">size</td>
                    <td className="p-2">"small" | "normal" | "large" | "icon"</td>
                    <td className="p-2">"normal"</td>
                    <td className="p-2">The size of the button</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">shape</td>
                    <td className="p-2">"default" | "rounded"</td>
                    <td className="p-2">"default"</td>
                    <td className="p-2">The border radius style</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">loading</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Shows loading spinner</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">disabled</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Disables the button</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-2 font-mono">fullWidth</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Makes button full width</td>
                  </tr>
                  <tr>
                    <td className="p-2 font-mono">fullHeight</td>
                    <td className="p-2">boolean</td>
                    <td className="p-2">false</td>
                    <td className="p-2">Makes button full height</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
