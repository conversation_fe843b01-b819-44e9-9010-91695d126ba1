"use client";

import { Badge } from "@ourtrip/ui";
import { ComponentPreview } from "@/components/component-preview";
import { CodeBlock } from "@/components/code-block";

export default function BadgePage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Badge</h1>
        <p className="text-xl text-muted-foreground">
          A small status indicator for displaying counts, labels, or status information.
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Import</h2>
        <CodeBlock code={`import { Badge } from "@ourtrip/ui";`} />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Usage</h2>
        <ComponentPreview
          code={`<Badge>New</Badge>`}
        >
          <Badge>New</Badge>
        </ComponentPreview>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Examples</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Basic Badges</h3>
            <ComponentPreview
              code={`<div className="flex gap-2">
  <Badge>Default</Badge>
  <Badge>New</Badge>
  <Badge>Hot</Badge>
  <Badge>Sale</Badge>
</div>`}
            >
              <div className="flex gap-2">
                <Badge>Default</Badge>
                <Badge>New</Badge>
                <Badge>Hot</Badge>
                <Badge>Sale</Badge>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">With Numbers</h3>
            <ComponentPreview
              code={`<div className="flex gap-2">
  <Badge>1</Badge>
  <Badge>99</Badge>
  <Badge>999+</Badge>
</div>`}
            >
              <div className="flex gap-2">
                <Badge>1</Badge>
                <Badge>99</Badge>
                <Badge>999+</Badge>
              </div>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">In Context</h3>
            <ComponentPreview
              code={`<div className="space-y-4">
  <div className="flex items-center gap-2">
    <span>Messages</span>
    <Badge>3</Badge>
  </div>
  <div className="flex items-center gap-2">
    <span>Notifications</span>
    <Badge>12</Badge>
  </div>
  <div className="flex items-center gap-2">
    <span>Updates</span>
    <Badge>New</Badge>
  </div>
</div>`}
            >
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span>Messages</span>
                  <Badge>3</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span>Notifications</span>
                  <Badge>12</Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span>Updates</span>
                  <Badge>New</Badge>
                </div>
              </div>
            </ComponentPreview>
          </div>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">API Reference</h2>
        <div className="rounded-lg border">
          <div className="p-6">
            <h3 className="text-lg font-medium mb-4">Props</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-medium">Prop</th>
                    <th className="text-left p-2 font-medium">Type</th>
                    <th className="text-left p-2 font-medium">Default</th>
                    <th className="text-left p-2 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody className="text-muted-foreground">
                  <tr className="border-b">
                    <td className="p-2 font-mono">children</td>
                    <td className="p-2">ReactNode</td>
                    <td className="p-2">-</td>
                    <td className="p-2">The content to display inside the badge</td>
                  </tr>
                  <tr>
                    <td className="p-2 font-mono">className</td>
                    <td className="p-2">string</td>
                    <td className="p-2">-</td>
                    <td className="p-2">Additional CSS classes</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              The Badge component also accepts all standard HTML span attributes.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
