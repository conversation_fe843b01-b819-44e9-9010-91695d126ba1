"use client";

import { <PERSON>, <PERSON><PERSON> } from "@ourtrip/ui";
import { ComponentPreview } from "@/components/component-preview";
import { CodeBlock } from "@/components/code-block";

export default function CardPage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Card</h1>
        <p className="text-xl text-muted-foreground">
          A flexible container component for grouping related content.
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Import</h2>
        <CodeBlock code={`import { Card } from "@ourtrip/ui";`} />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Usage</h2>
        <ComponentPreview
          code={`<Card>
  <h3 className="text-lg font-semibold mb-2">Card Title</h3>
  <p className="text-muted-foreground">
    This is a simple card component with some content inside.
  </p>
</Card>`}
        >
          <Card>
            <h3 className="text-lg font-semibold mb-2">Card Title</h3>
            <p className="text-muted-foreground">
              This is a simple card component with some content inside.
            </p>
          </Card>
        </ComponentPreview>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Examples</h2>
        
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Card with Actions</h3>
            <ComponentPreview
              code={`<Card>
  <div className="space-y-4">
    <div>
      <h3 className="text-lg font-semibold">Product Card</h3>
      <p className="text-muted-foreground">
        A beautiful product with amazing features.
      </p>
    </div>
    <div className="flex gap-2">
      <Button size="small">Buy Now</Button>
      <Button variant="outline" size="small">Learn More</Button>
    </div>
  </div>
</Card>`}
            >
              <Card>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-semibold">Product Card</h3>
                    <p className="text-muted-foreground">
                      A beautiful product with amazing features.
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button size="small">Buy Now</Button>
                    <Button variant="outline" size="small">Learn More</Button>
                  </div>
                </div>
              </Card>
            </ComponentPreview>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Multiple Cards</h3>
            <ComponentPreview
              code={`<div className="grid gap-4 md:grid-cols-2">
  <Card>
    <h3 className="text-lg font-semibold mb-2">Card 1</h3>
    <p className="text-muted-foreground">First card content.</p>
  </Card>
  <Card>
    <h3 className="text-lg font-semibold mb-2">Card 2</h3>
    <p className="text-muted-foreground">Second card content.</p>
  </Card>
</div>`}
            >
              <div className="grid gap-4 md:grid-cols-2 w-full">
                <Card>
                  <h3 className="text-lg font-semibold mb-2">Card 1</h3>
                  <p className="text-muted-foreground">First card content.</p>
                </Card>
                <Card>
                  <h3 className="text-lg font-semibold mb-2">Card 2</h3>
                  <p className="text-muted-foreground">Second card content.</p>
                </Card>
              </div>
            </ComponentPreview>
          </div>
        </div>
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">API Reference</h2>
        <div className="rounded-lg border">
          <div className="p-6">
            <h3 className="text-lg font-medium mb-4">Props</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2 font-medium">Prop</th>
                    <th className="text-left p-2 font-medium">Type</th>
                    <th className="text-left p-2 font-medium">Default</th>
                    <th className="text-left p-2 font-medium">Description</th>
                  </tr>
                </thead>
                <tbody className="text-muted-foreground">
                  <tr className="border-b">
                    <td className="p-2 font-mono">children</td>
                    <td className="p-2">ReactNode</td>
                    <td className="p-2">-</td>
                    <td className="p-2">The content to display inside the card</td>
                  </tr>
                  <tr>
                    <td className="p-2 font-mono">className</td>
                    <td className="p-2">string</td>
                    <td className="p-2">-</td>
                    <td className="p-2">Additional CSS classes</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              The Card component also accepts all standard HTML div attributes.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
