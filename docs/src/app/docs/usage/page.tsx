"use client";

import { CodeBlock } from "@/components/code-block";

export default function UsagePage() {
  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Usage</h1>
        <p className="text-xl text-muted-foreground">
          Learn how to use OurTrip UI components in your React applications.
        </p>
      </div>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Basic Usage</h2>
        <p className="text-muted-foreground">
          Import components from the library and use them in your JSX:
        </p>
        <CodeBlock
          code={`import { Button, Card, Input } from "@ourtrip/ui";

function MyComponent() {
  return (
    <Card>
      <h2>Sign Up</h2>
      <Input label="Email" type="email" placeholder="Enter your email" />
      <Input label="Password" type="password" placeholder="Enter your password" />
      <Button fullWidth>Create Account</Button>
    </Card>
  );
}`}
        />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">TypeScript Support</h2>
        <p className="text-muted-foreground">
          OurTrip UI is built with TypeScript and provides full type definitions:
        </p>
        <CodeBlock
          language="typescript"
          code={`import { Button, ButtonProps } from "@ourtrip/ui";

interface MyButtonProps extends ButtonProps {
  customProp?: string;
}

function MyButton({ customProp, ...props }: MyButtonProps) {
  return <Button {...props} />;
}`}
        />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Styling and Customization</h2>
        <p className="text-muted-foreground">
          Components are built with Tailwind CSS and can be customized using the className prop:
        </p>
        <CodeBlock
          code={`import { Button } from "@ourtrip/ui";

function CustomButton() {
  return (
    <Button 
      className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
    >
      Gradient Button
    </Button>
  );
}`}
        />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Form Handling</h2>
        <p className="text-muted-foreground">
          Components work seamlessly with form libraries like React Hook Form:
        </p>
        <CodeBlock
          code={`import { useForm } from "react-hook-form";
import { Button, Input, Card } from "@ourtrip/ui";

function ContactForm() {
  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmit = (data) => {
    console.log(data);
  };

  return (
    <Card>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Input
          label="Name"
          {...register("name", { required: "Name is required" })}
          error={errors.name?.message}
        />
        <Input
          label="Email"
          type="email"
          {...register("email", { required: "Email is required" })}
          error={errors.email?.message}
        />
        <Button type="submit" fullWidth>
          Submit
        </Button>
      </form>
    </Card>
  );
}`}
        />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Validation Utilities</h2>
        <p className="text-muted-foreground">
          The library includes validation utilities for common use cases:
        </p>
        <CodeBlock
          code={`import { isValidEmail, isValidCpf, cn } from "@ourtrip/ui";

function validateForm(data) {
  const errors = {};
  
  if (!isValidEmail(data.email)) {
    errors.email = "Please enter a valid email";
  }
  
  if (!isValidCpf(data.cpf)) {
    errors.cpf = "Please enter a valid CPF";
  }
  
  return errors;
}

// Using the cn utility for conditional classes
function MyComponent({ isActive }) {
  return (
    <div className={cn(
      "base-class",
      isActive && "active-class",
      "another-class"
    )}>
      Content
    </div>
  );
}`}
        />
      </section>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold tracking-tight">Best Practices</h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">1. Import only what you need</h3>
            <p className="text-muted-foreground mb-2">
              Import components individually to optimize bundle size:
            </p>
            <CodeBlock
              code={`// Good
import { Button } from "@ourtrip/ui";

// Avoid importing everything
import * as UI from "@ourtrip/ui";`}
            />
          </div>

          <div>
            <h3 className="text-lg font-medium">2. Use semantic HTML</h3>
            <p className="text-muted-foreground mb-2">
              Components render semantic HTML elements. Use appropriate components for their intended purpose:
            </p>
            <CodeBlock
              code={`// Use Button for actions
<Button onClick={handleClick}>Submit</Button>

// Use Input for form fields
<Input label="Email" type="email" />

// Use Card for content grouping
<Card>
  <h2>Title</h2>
  <p>Content</p>
</Card>`}
            />
          </div>

          <div>
            <h3 className="text-lg font-medium">3. Accessibility</h3>
            <p className="text-muted-foreground mb-2">
              Components include accessibility features by default. Enhance them with proper labels and ARIA attributes:
            </p>
            <CodeBlock
              code={`<Input 
  label="Search" 
  placeholder="Search products..."
  aria-describedby="search-help"
/>
<div id="search-help" className="text-sm text-muted-foreground">
  Enter keywords to search for products
</div>`}
            />
          </div>
        </div>
      </section>
    </div>
  );
}
