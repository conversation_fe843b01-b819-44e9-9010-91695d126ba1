"use client";

import * as React from "react";
import { Button, Input, Checkbox } from "@ourtrip/ui";
import { ComponentPreview } from "./component-preview";

interface PlaygroundControl {
  name: string;
  type: "select" | "boolean" | "text" | "number";
  options?: string[];
  defaultValue?: any;
  label: string;
}

interface ComponentPlaygroundProps {
  component: React.ComponentType<any>;
  controls: PlaygroundControl[];
  defaultProps?: Record<string, any>;
  children?: React.ReactNode;
}

export function ComponentPlayground({
  component: Component,
  controls,
  defaultProps = {},
  children,
}: ComponentPlaygroundProps) {
  const [props, setProps] = React.useState(() => {
    const initialProps = { ...defaultProps };
    controls.forEach((control) => {
      if (control.defaultValue !== undefined) {
        initialProps[control.name] = control.defaultValue;
      }
    });
    return initialProps;
  });

  const updateProp = (name: string, value: any) => {
    setProps((prev) => ({ ...prev, [name]: value }));
  };

  const generateCode = () => {
    const propsString = Object.entries(props)
      .filter(([_, value]) => value !== undefined && value !== "" && value !== false)
      .map(([key, value]) => {
        if (typeof value === "boolean") {
          return value ? key : "";
        }
        if (typeof value === "string") {
          return `${key}="${value}"`;
        }
        return `${key}={${JSON.stringify(value)}}`;
      })
      .filter(Boolean)
      .join(" ");

    const componentName = Component.displayName || Component.name || "Component";
    
    if (children) {
      return `<${componentName}${propsString ? ` ${propsString}` : ""}>
  ${children}
</${componentName}>`;
    }
    
    return `<${componentName}${propsString ? ` ${propsString}` : ""} />`;
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Controls</h3>
          <div className="space-y-4 p-4 border rounded-lg">
            {controls.map((control) => (
              <div key={control.name} className="space-y-2">
                <label className="text-sm font-medium">{control.label}</label>
                {control.type === "select" && (
                  <select
                    value={props[control.name] || ""}
                    onChange={(e) => updateProp(control.name, e.target.value)}
                    className="w-full rounded-inner h-[40px] px-4 border border-gray-200 bg-white"
                  >
                    <option value="">None</option>
                    {control.options?.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                )}
                {control.type === "boolean" && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={control.name}
                      checked={props[control.name] || false}
                      onCheckedChange={(checked) => updateProp(control.name, checked)}
                    />
                    <label htmlFor={control.name} className="text-sm">
                      {control.label}
                    </label>
                  </div>
                )}
                {control.type === "text" && (
                  <Input
                    value={props[control.name] || ""}
                    onChange={(e) => updateProp(control.name, e.target.value)}
                    placeholder={`Enter ${control.label.toLowerCase()}`}
                  />
                )}
                {control.type === "number" && (
                  <Input
                    type="number"
                    value={props[control.name] || ""}
                    onChange={(e) => updateProp(control.name, Number(e.target.value))}
                    placeholder={`Enter ${control.label.toLowerCase()}`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Preview</h3>
          <ComponentPreview code={generateCode()}>
            <Component {...props}>
              {children}
            </Component>
          </ComponentPreview>
        </div>
      </div>
    </div>
  );
}
