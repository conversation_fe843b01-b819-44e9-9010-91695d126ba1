"use client";

import * as React from "react";
import { Copy, Check } from "lucide-react";
import { Button } from "@ourtrip/ui";

interface CodeBlockProps {
  code: string;
  language?: string;
  title?: string;
}

export function CodeBlock({ code, language = "tsx", title }: CodeBlockProps) {
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative">
      {title && (
        <div className="flex items-center justify-between rounded-t-lg border border-b-0 bg-muted px-4 py-2">
          <span className="text-sm font-medium">{title}</span>
        </div>
      )}
      <div className="relative">
        <pre className={`bg-muted p-4 overflow-x-auto ${title ? 'rounded-t-none' : 'rounded-lg'}`}>
          <code className="text-sm">{code}</code>
        </pre>
        <Button
          size="icon"
          variant="outline"
          className="absolute right-4 top-4 h-8 w-8"
          onClick={copyToClipboard}
        >
          {copied ? (
            <Check className="h-4 w-4" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
          <span className="sr-only">Copy</span>
        </Button>
      </div>
    </div>
  );
}
