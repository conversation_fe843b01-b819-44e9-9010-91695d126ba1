"use client";

import * as React from "react";
import { Copy, Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface ComponentPreviewProps {
  children: React.ReactNode;
  code: string;
  className?: string;
}

export function ComponentPreview({
  children,
  code,
  className,
}: ComponentPreviewProps) {
  const [activeTab, setActiveTab] = React.useState("preview");
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = async () => {
    await navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative">
      <div className="relative mr-auto w-full">
        <div className="flex items-center justify-between pb-3">
          <div className="w-full justify-start rounded-none border-b bg-transparent p-0">
            <button
              onClick={() => setActiveTab("preview")}
              className={cn(
                "relative h-9 rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold shadow-none transition-none",
                activeTab === "preview"
                  ? "border-b-primary text-foreground"
                  : "text-muted-foreground"
              )}
            >
              Preview
            </button>
            <button
              onClick={() => setActiveTab("code")}
              className={cn(
                "relative h-9 rounded-none border-b-2 border-b-transparent bg-transparent px-4 pb-3 pt-2 font-semibold shadow-none transition-none",
                activeTab === "code"
                  ? "border-b-primary text-foreground"
                  : "text-muted-foreground"
              )}
            >
              Code
            </button>
          </div>
        </div>

        {activeTab === "preview" && (
          <div className="relative rounded-md border">
            <div className={cn("preview flex min-h-[350px] w-full justify-center p-10", className)}>
              {children}
            </div>
          </div>
        )}

        {activeTab === "code" && (
          <div className="flex flex-col space-y-4">
            <div className="w-full rounded-md [&_pre]:my-0 [&_pre]:max-h-[350px] [&_pre]:overflow-auto">
              <div className="relative">
                <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                  <code className="text-sm">{code}</code>
                </pre>
                <button
                  className="absolute right-4 top-4 h-8 w-8 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground"
                  onClick={copyToClipboard}
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  <span className="sr-only">Copy</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
