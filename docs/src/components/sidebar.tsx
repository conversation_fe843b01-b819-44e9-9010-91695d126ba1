"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@radix-ui/react-scroll-area";

const sidebarNavItems = [
  {
    title: "Getting Started",
    items: [
      {
        title: "Installation",
        href: "/docs/installation",
      },
      {
        title: "Usage",
        href: "/docs/usage",
      },
    ],
  },
  {
    title: "Components",
    items: [
      {
        title: "Accordion",
        href: "/docs/components/accordion",
      },
      {
        title: "Alert",
        href: "/docs/components/alert",
      },
      {
        title: "Badge",
        href: "/docs/components/badge",
      },
      {
        title: "Breadcrumbs",
        href: "/docs/components/breadcrumbs",
      },
      {
        title: "Button",
        href: "/docs/components/button",
      },
      {
        title: "Card",
        href: "/docs/components/card",
      },
      {
        title: "Checkbox",
        href: "/docs/components/checkbox",
      },
      {
        title: "Collapse",
        href: "/docs/components/collapse",
      },
      {
        title: "Divider",
        href: "/docs/components/divider",
      },
      {
        title: "Input",
        href: "/docs/components/input",
      },
      {
        title: "Modal",
        href: "/docs/components/modal",
      },
      {
        title: "Pagination",
        href: "/docs/components/pagination",
      },
      {
        title: "Phone Input",
        href: "/docs/components/phone",
      },
      {
        title: "Popover",
        href: "/docs/components/popover",
      },
      {
        title: "Radio",
        href: "/docs/components/radio",
      },
      {
        title: "Range",
        href: "/docs/components/range",
      },
      {
        title: "Select",
        href: "/docs/components/select",
      },
      {
        title: "Sheet",
        href: "/docs/components/sheet",
      },
      {
        title: "Stars",
        href: "/docs/components/stars",
      },
      {
        title: "Step Marker",
        href: "/docs/components/step-marker",
      },
      {
        title: "Switch",
        href: "/docs/components/switch",
      },
      {
        title: "Tag",
        href: "/docs/components/tag",
      },
      {
        title: "Tooltip",
        href: "/docs/components/tooltip",
      },
    ],
  },
];

export function Sidebar() {
  const pathname = usePathname();

  return (
    <div className="w-64 border-r bg-background">
      <ScrollArea className="h-[calc(100vh-3.5rem)] py-6 pl-8 pr-6">
        <div className="space-y-4">
          {sidebarNavItems.map((section) => (
            <div key={section.title}>
              <h4 className="mb-2 px-2 py-1 text-sm font-semibold tracking-tight">
                {section.title}
              </h4>
              <div className="space-y-1">
                {section.items.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "block select-none space-y-1 rounded-md p-2 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
                      pathname === item.href
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground"
                    )}
                  >
                    <div className="text-sm font-medium leading-none">
                      {item.title}
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
