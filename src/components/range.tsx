'use client';

import React, { memo, useState } from 'react';
import ReactSlider from 'react-slider';

interface IRange {
  initialValues?: number[];
  min?: number;
  max?: number;
  onChange?: (value: number[]) => void;
  disabled?: boolean;
}

const Range = ({
  min,
  max,
  onChange,
  disabled = false,
  initialValues
}: IRange) => {
  const [rangeValue, setRangeValue] = useState<number[]>(
    initialValues ?? [min || 0, max || 1000]
  );

  return (
    <div className='w-full flex flex-col gap-[5px]'>
      <p className='text-primary-900 text-sm select-none'>
        {`${rangeValue[0]} - ${rangeValue[1]}`}
      </p>
      <ReactSlider
        className='slider'
        disabled={disabled}
        min={min || 1}
        max={max || 1000}
        value={initialValues}
        onChange={value => setRangeValue(value)}
        ariaLabel={['Lower thumb', 'Upper thumb']}
        onAfterChange={value => {
          onChange?.(value);
        }}
        minDistance={10}
        pearling
      />
    </div>
  );
};

const propsAreEqual = (prevProps: IRange, nextProps: IRange) =>
  prevProps.min === nextProps.min &&
  prevProps.max === nextProps.max &&
  prevProps.disabled === nextProps.disabled &&
  prevProps.initialValues === nextProps.initialValues;

export default memo(Range, propsAreEqual);
