'use client';

import React, { FC, ReactNode } from 'react';
import { Check, Info, Warning, WarningCircle } from '@phosphor-icons/react';

type TAlertType = 'info' | 'success' | 'danger' | 'warning';
type TAlertVariant = 'fill' | 'outline';

interface IAlertProps {
  type: TAlertType;
  variant: TAlertVariant;
  children: ReactNode;
}

const iconByAlertType: Record<TAlertType, ReactNode> = {
  danger: <WarningCircle size={24} />,
  success: <Check size={24} />,
  info: <Info size={24} />,
  warning: <Warning size={24} />
};

const Alert: FC<IAlertProps> = ({ type, variant, children }) => {
  const baseClasses = 'rounded-default p-4 flex items-start gap-4';
  const typeVariantClasses: Record<
    TAlertType,
    Record<TAlertVariant, string>
  > = {
    info: {
      fill: 'bg-info-500 text-white',
      outline: 'border border-info-500 text-info-500'
    },
    success: {
      fill: 'bg-success-500 text-white',
      outline: 'border border-success-500 text-success-500'
    },
    danger: {
      fill: 'bg-danger-500 text-white',
      outline: 'border border-danger-500 text-danger-500'
    },
    warning: {
      fill: 'bg-warning-500 text-white',
      outline: 'border border-warning-500 text-warning-500'
    }
  };

  const className = `${baseClasses} ${typeVariantClasses[type][variant]}`;

  return (
    <div className={className}>
      {iconByAlertType[type]}
      <div>{children}</div>
    </div>
  );
};

export default Alert;
