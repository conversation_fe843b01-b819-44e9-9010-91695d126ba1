'use client';

import * as React from 'react';
import { CircleNotch } from '@phosphor-icons/react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/classes';

const buttonVariants = cva(
  'flex items-center justify-center gap-2 whitespace-nowrap transition-colors focus:outline-hidden',
  {
    variants: {
      variant: {
        fill: '',
        outline: 'bg-transparent border'
      },
      color: {
        primary: '',
        secondary: '',
        danger: '',
        gray: '',
        white: ''
      },
      size: {
        small: 'h-[32px] px-4 text-sm',
        normal: 'h-[40px] px-6 text-base',
        large: 'h-[48px] px-8 text-lg',
        icon: 'h-[40px] w-[40px] p-0'
      },
      shape: {
        default: 'rounded-inner',
        rounded: 'rounded-full'
      },
      fontWeight: {
        normal: 'font-normal',
        bold: 'font-medium'
      },
      fullWidth: {
        true: 'w-full',
        false: ''
      },
      fullHeight: {
        true: 'h-full',
        false: ''
      },
      disabled: {
        true: 'bg-gray-200 text-gray-900 border-none cursor-not-allowed hover:bg-gray-200 hover:text-gray-900',
        false: ''
      }
    },
    compoundVariants: [
      {
        variant: 'fill',
        color: 'primary',
        disabled: false,
        className: 'bg-primary-500 text-white hover:bg-primary-400 active:bg-primary-600'
      },
      {
        variant: 'fill',
        color: 'secondary',
        disabled: false,
        className: 'bg-secondary-500 text-white hover:bg-secondary-400 active:bg-secondary-600'
      },
      {
        variant: 'fill',
        color: 'danger',
        disabled: false,
        className: 'bg-red-500 text-white hover:bg-red-400 active:bg-red-600'
      },
      {
        variant: 'fill',
        color: 'gray',
        disabled: false,
        className: 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300'
      },
      {
        variant: 'fill',
        color: 'white',
        disabled: false,
        className: 'bg-white text-black hover:bg-gray-200'
      },
      {
        variant: 'outline',
        color: 'primary',
        disabled: false,
        className:
          'text-primary-500 border-primary-500 hover:text-primary-400 hover:border-primary-400'
      },
      {
        variant: 'outline',
        color: 'secondary',
        disabled: false,
        className:
          'text-secondary-500 border-secondary-500 hover:text-secondary-400 hover:border-secondary-400'
      },
      {
        variant: 'outline',
        color: 'danger',
        disabled: false,
        className:
          'text-red-500 border-red-500 hover:text-red-400 hover:border-red-400'
      },
      {
        variant: 'outline',
        color: 'gray',
        disabled: false,
        className:
          'text-gray-300 border-gray-300 hover:text-gray-500 hover:border-gray-400'
      },
      {
        variant: 'outline',
        color: 'white',
        disabled: false,
        className:
          'text-white border-white hover:text-gray-200 hover:border-gray-200'
      }
    ],
    defaultVariants: {
      variant: 'fill',
      color: 'primary',
      size: 'normal',
      shape: 'default',
      fontWeight: 'normal',
      fullWidth: false,
      fullHeight: false,
      disabled: false
    }
  }
);

interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
  color?: 'primary' | 'secondary' | 'danger' | 'gray' | 'white';
  disabled?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      color,
      size,
      shape,
      fontWeight,
      fullWidth,
      fullHeight,
      disabled,
      loading = false,
      children,
      ...props
    },
    ref
  ) => {
    return (
      <button
        className={cn(
          buttonVariants({
            variant,
            color,
            size,
            shape,
            fontWeight,
            fullWidth,
            fullHeight,
            disabled: disabled || loading
          }),
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <CircleNotch className='animate-spin' weight='bold' />
        ) : (
          children
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
