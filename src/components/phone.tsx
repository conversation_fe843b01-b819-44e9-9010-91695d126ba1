'use client';

import React from 'react';
import Input from './input';
import { isValidDDD, isValidPhoneNumber } from '../utils/validation';
import {
  FieldErrors,
  FieldValues,
  Path,
  UseFormSetFocus
} from 'react-hook-form';

interface IPhoneInputProps<T extends FieldValues> {
  DDI?: string;
  phoneDDIKey: Path<T>;
  phoneAreaCodeKey: Path<T>;
  phoneNumberKey: Path<T>;
  registerWithMask: any;
  setFocus: UseFormSetFocus<T>;
  errors: FieldErrors<T>;
  ddiClassName?: string;
  areaCodeClassName?: string;
  phoneClassName?: string;
  requiredMessage?: string;
  ddiLabel?: string;
  areaCodeLabel?: string;
  numberLabel?: string;
  invalidAreaCodeMessage?: string;
  invalidPhoneNumberMessage?: string;
}

const PhoneInput = <T extends FieldValues>({
  DDI,
  phoneDDIKey,
  phoneAreaCodeKey,
  phoneNumberKey,
  registerWithMask,
  setFocus,
  errors,
  ddiClassName,
  areaCodeClassName,
  phoneClassName,
  requiredMessage,
  ddiLabel,
  areaCodeLabel,
  numberLabel,
  invalidAreaCodeMessage,
  invalidPhoneNumberMessage
}: IPhoneInputProps<T>) => {
  const host = typeof window !== 'undefined' ? window.location.host : '';

  return (
    <div className='w-full flex flex-col gap-1'>
      <div className='flex flex-col md:flex-row gap-0.5'>
        <Input
          className={`flex-2 rounded-b-none md:rounded-r-none md:rounded-l-inner ${ddiClassName}`}
          placeholder={ddiLabel}
          {...registerWithMask(phoneDDIKey, ['+9', '+99', '+999'], {
            value: DDI,
            required: requiredMessage
          })}
          defaultValue={host?.includes('.br') ? '+55' : ''}
          color='gray'
        />
        <Input
          className={`flex-2 rounded-none ${areaCodeClassName}`}
          placeholder={areaCodeLabel}
          {...registerWithMask(phoneAreaCodeKey, DDI === '+55' ? '(99)' : '', {
            required: requiredMessage,
            validate: (value: any) =>
              DDI === '+55'
                ? isValidDDD(value)
                : true || invalidAreaCodeMessage,
            oncomplete: () => {
              if (DDI === '+55') {
                setFocus(phoneNumberKey);
              }
            }
          })}
          color='gray'
        />
        <Input
          className={`flex-5 rounded-t-none md:rounded-l-none md:rounded-r-inner ${phoneClassName}`}
          placeholder={numberLabel}
          {...registerWithMask(
            phoneNumberKey,
            DDI === '+55' ? '99999-9999' : '',
            {
              required: requiredMessage,
              validate: (value: any) =>
                DDI === '+55'
                  ? isValidPhoneNumber(value)
                  : true || invalidPhoneNumberMessage,
            }
          )}
          color='gray'
        />
      </div>
      {((errors?.phone as any)?.ddi?.message ||
        (errors?.phone as any)?.areaCode?.message ||
        (errors?.phone as any)?.number?.message) && (
        <span className='text-red-600 text-sm'>
          {(errors?.phone as any)?.ddi?.message ||
            (errors?.phone as any)?.areaCode?.message ||
            (errors?.phone as any)?.number?.message}
        </span>
      )}
    </div>
  );
};

export default PhoneInput;
