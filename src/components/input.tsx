'use client';

/* eslint-disable no-param-reassign */
/* eslint-disable react-hooks/rules-of-hooks */
import React, {
  DetailedHTMLProps,
  InputHTMLAttributes,
  forwardRef,
  useRef
} from 'react';

interface IInput
  extends DetailedHTMLProps<
    InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  > {
  label?: string;
  dense?: boolean;
  floating?: boolean;
  border?: boolean;
  color?: 'white' | 'gray';
  error?: string;
  focus?: Function;
  mask?: Function;
  hasInitialValue?: boolean;
}

const Input = forwardRef(
  (
    {
      id,
      label,
      placeholder,
      color = 'white',
      dense,
      floating,
      border,
      onChange,
      onFocus,
      onBlur,
      focus,
      error,
      disabled,
      ...props
    }: IInput,
    ref: any
  ) => {
    const inputRef = useRef<HTMLInputElement | null>(null);

    return (
      <div
        aria-labelledby={id}
        onClick={() => inputRef.current?.focus()}
        className={`flex flex-col w-full ${props.className}`}
      >
        <div
          onClick={() => {
            if (ref?.current) {
              ref.current?.focus();
            } else {
              focus?.();
            }
          }}
        >
          {label && (
            <label htmlFor={id} className='mb-2 text-sm text-gray-500'>
              {label}
            </label>
          )}
          <input
            id={id}
            ref={e => {
              if (typeof ref === 'function') ref(e);
              else if (ref && typeof ref === 'object') ref.current = e;
              inputRef.current = e;
            }}
            placeholder={placeholder}
            autoComplete='off'
            {...props}
            disabled={disabled}
            onChange={event => {
              onChange?.(event);
            }}
            onFocus={event => {
              onFocus?.(event);
            }}
            onBlur={e => {
              onBlur?.(e);
            }}
            className={`${
              color === 'white'
                ? 'bg-white disabled:bg-white'
                : 'bg-gray-100 disabled:bg-gray-100'
            } w-full rounded-inner h-[50px] px-4 disabled:cursor-not-allowed placeholder-gray-500 text-primary-900 ${
              props.className
            }`}
          />
        </div>
        {error && <span className='text-sm text-red-600'>{error}</span>}
      </div>
    );
  }
);

export default Input;
