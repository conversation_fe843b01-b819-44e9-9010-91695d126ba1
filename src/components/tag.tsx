'use client';

import React, { FC, ReactNode } from 'react';

export type TagColor = 'primary' | 'secondary' | 'white';

interface ITagProps {
  color: TagColor;
  children: ReactNode;
  onClick?: () => void;
  iconLeft?: ReactNode;
  iconRight?: ReactNode;
}

const Tag: FC<ITagProps> = ({
  color,
  children,
  onClick,
  iconLeft,
  iconRight
}) => {
  const baseClasses =
    'rounded-inner px-4 py-2 flex items-center justify-between border-0 cursor-pointer transition-all duration-300 ease-in-out font-medium';

  const colorClasses = {
    primary: 'text-primary-900 bg-white hover:bg-gray-200',
    secondary:
      'text-secondary-800 bg-secondary-100 hover:text-white hover:bg-secondary-800',
    white: 'text-primary-900 bg-white hover:bg-gray-200'
  };

  return (
    <button
      onClick={() => onClick?.()}
      className={`${baseClasses} ${colorClasses[color]}`}
    >
      {iconLeft && (
        <span className='flex items-center justify-center mr-2'>
          {iconLeft}
        </span>
      )}
      {children}
      {iconRight && (
        <span className='flex items-center justify-center ml-2'>
          {iconRight}
        </span>
      )}
    </button>
  );
};

export default Tag;
