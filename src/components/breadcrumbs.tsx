'use client';

/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/anchor-is-valid */
import React from 'react';
import { CaretRight } from '@phosphor-icons/react';

export type BreadcrumbsItemType = {
  id: string;
  text: string;
  href: string;
};

interface IBreadcrumbsProps {
  items: BreadcrumbsItemType[];
}

const Breadcrumbs = ({ items }: IBreadcrumbsProps) => {
  return (
    <div className='flex items-center gap-2.5'>
      {items?.map((item, index) => {
        const isActive = index !== items.length - 1;
        return (
          <div key={item.id} className='flex items-center gap-2.5'>
            <a
              onClick={() => {
                if (isActive) {
                  window.open(item.href, '_blank');
                }
              }}
              className={`no-underline text-blue-900 text-sm ${
                isActive
                  ? 'hover:text-blue-500 hover:cursor-pointer'
                  : 'cursor-text'
              }`}
            >
              {item.text}
            </a>
            {isActive && <CaretRight />}
          </div>
        );
      })}
    </div>
  );
};

export default Breadcrumbs;
