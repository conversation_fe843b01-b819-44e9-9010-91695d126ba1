'use client';

import React, { useEffect, useState } from 'react';
import { CaretLeft, CaretRight } from '@phosphor-icons/react';
import { cva } from 'class-variance-authority';
import { cn } from '../utils/classes';

const paginationItemVariants = cva(
  'w-10 h-10 flex items-center justify-center rounded-inner text-primary-900 transition-colors cursor-pointer',
  {
    variants: {
      selected: {
        true: 'bg-primary-900 text-white hover:bg-primary-900',
        false: ''
      },
      disabled: {
        true: 'hover:text-primary-900',
        false: ''
      },
      prevOrNext: {
        true: 'bg-white hover:bg-primary-500 hover:text-white',
        false: 'hover:bg-gray-200'
      }
    },
    defaultVariants: {
      selected: false,
      disabled: false,
      prevOrNext: false
    }
  }
);

interface IPagination {
  page: number;
  total: number;
  loading?: boolean;
  onChange?: (value: number) => void;
}

const Pagination = ({
  page,
  total,
  loading = false,
  onChange
}: IPagination) => {
  const [items, setItems] = useState<number[]>([]);

  useEffect(() => {
    if (page && total) {
      setItems([1]);

      const firstItem = page - 2 > 1 ? page - 2 : 1;
      const lastItem = page + 1 < total ? page + 1 : total - 1;

      if (firstItem !== 1) setItems(prev => [...prev, 0]);
      for (let index = firstItem; index < lastItem; index++) {
        setItems(prev => [...prev, index + 1]);
      }
      if (lastItem + 1 !== total) setItems(prev => [...prev, 0]);

      setItems(prev => [...prev, total]);
    }
  }, [page, total]);

  return (
    <div className='w-full flex items-center justify-between'>
      <div
        className={cn(
          paginationItemVariants({
            disabled: loading || page === 1,
            prevOrNext: true
          })
        )}
        onClick={() => !loading && page !== 1 && onChange?.(page - 1)}
      >
        <CaretLeft weight='bold' />
      </div>
      <div className='flex items-center justify-between gap-2.5'>
        {items.map((value, index) => (
          <div
            key={index}
            className={cn(
              paginationItemVariants({
                selected: value === page,
                disabled: loading || !value,
                prevOrNext: false
              })
            )}
            onClick={() => !loading && value && onChange?.(value)}
          >
            {value || '...'}
          </div>
        ))}
      </div>
      <div
        className={cn(
          paginationItemVariants({
            disabled: loading || page === total,
            prevOrNext: true
          })
        )}
        onClick={() => !loading && page !== total && onChange?.(page + 1)}
      >
        <CaretRight weight='bold' />
      </div>
    </div>
  );
};

export default Pagination;
